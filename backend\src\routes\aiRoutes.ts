import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { 
  AudioTranscriptionRequestSchema,
  AiAnalysisRequestSchema,
  VoiceToReportRequestSchema,
  type AudioTranscriptionResponse,
  type AiAnalysisResponse,
  type VoiceToReportResponse
} from '../types/schemas/aiSchemas';
import { AiService } from '../services/aiService';
import { AudioTranscriptionService } from '../services/audioTranscriptionService';

/**
 * AI Routes for voice-powered bug reporting
 * Handles audio transcription and AI analysis endpoints
 */
export function createAiRoutes(): Hono {
  const router = new Hono();
  
  // Initialize services
  const aiService = new AiService();
  const transcriptionService = new AudioTranscriptionService();

  /**
   * GET /ai/status
   * Check AI service availability
   */
  router.get('/status', async (c) => {
    try {
      const status = {
        aiServiceAvailable: aiService.isAvailable(),
        transcriptionServiceAvailable: transcriptionService.isAvailable(),
        supportedAudioFormats: AudioTranscriptionService.getSupportedFormats(),
        bestAudioFormat: AudioTranscriptionService.getBestSupportedFormat(),
        maxAudioDuration: 60, // seconds
        supportedLanguages: ['en', 'fa'],
        features: {
          transcription: transcriptionService.isAvailable(),
          aiAnalysis: aiService.isAvailable(),
          voiceToReport: transcriptionService.isAvailable() && aiService.isAvailable(),
        },
      };

      return c.json({
        success: true,
        status,
      });
    } catch (error: any) {
      console.error('[AiRoutes] Error checking AI status:', error);
      return c.json({
        success: false,
        error: 'Failed to check AI service status',
      }, 500);
    }
  });

  /**
   * POST /ai/transcribe
   * Transcribe audio to text
   */
  router.post(
    '/transcribe',
    zValidator('json', AudioTranscriptionRequestSchema),
    async (c) => {
      try {
        const requestData = c.req.valid('json');
        
        console.log(`[AiRoutes] Transcription request received - Duration: ${requestData.duration}s, Language: ${requestData.language}`);

        const result = await transcriptionService.transcribeAudio(requestData);
        
        const response: AudioTranscriptionResponse = result;
        
        if (result.success) {
          console.log(`[AiRoutes] Transcription successful - Confidence: ${result.confidence}, Length: ${result.transcription?.length} chars`);
        } else {
          console.warn(`[AiRoutes] Transcription failed: ${result.error}`);
        }

        return c.json(response);
      } catch (error: any) {
        console.error('[AiRoutes] Error in transcription endpoint:', error);
        return c.json({
          success: false,
          error: 'Internal server error during transcription',
        } as AudioTranscriptionResponse, 500);
      }
    }
  );

  /**
   * POST /ai/analyze
   * Analyze transcribed text and generate bug report
   */
  router.post(
    '/analyze',
    zValidator('json', AiAnalysisRequestSchema),
    async (c) => {
      try {
        const requestData = c.req.valid('json');
        
        console.log(`[AiRoutes] Analysis request received - Transcription length: ${requestData.transcription.length} chars, Language: ${requestData.language}`);

        const result = await aiService.analyzeTranscription(requestData);
        
        const response: AiAnalysisResponse = result;
        
        if (result.success) {
          console.log(`[AiRoutes] Analysis successful - Confidence: ${result.generatedReport?.confidence}, Processing time: ${result.processingTime}ms`);
        } else {
          console.warn(`[AiRoutes] Analysis failed: ${result.error}`);
        }

        return c.json(response);
      } catch (error: any) {
        console.error('[AiRoutes] Error in analysis endpoint:', error);
        return c.json({
          success: false,
          error: 'Internal server error during analysis',
        } as AiAnalysisResponse, 500);
      }
    }
  );

  /**
   * POST /ai/voice-to-report
   * Combined endpoint: transcribe audio and generate bug report
   */
  router.post(
    '/voice-to-report',
    zValidator('json', VoiceToReportRequestSchema),
    async (c) => {
      try {
        const requestData = c.req.valid('json');
        
        console.log(`[AiRoutes] Voice-to-report request received - Duration: ${requestData.duration}s, Language: ${requestData.language}`);

        const startTime = Date.now();

        // Step 1: Transcribe audio
        const transcriptionResult = await transcriptionService.transcribeAudio({
          audioData: requestData.audioData,
          mimeType: requestData.mimeType,
          duration: requestData.duration,
          language: requestData.language,
        });

        if (!transcriptionResult.success) {
          const response: VoiceToReportResponse = {
            success: false,
            error: `Transcription failed: ${transcriptionResult.error}`,
            processingTime: Date.now() - startTime,
          };
          return c.json(response);
        }

        // Step 2: Analyze transcription
        const analysisResult = await aiService.analyzeTranscription({
          transcription: transcriptionResult.transcription!,
          language: requestData.language,
          userContext: requestData.userContext,
        });

        if (!analysisResult.success) {
          const response: VoiceToReportResponse = {
            success: false,
            transcription: transcriptionResult.transcription,
            error: `Analysis failed: ${analysisResult.error}`,
            processingTime: Date.now() - startTime,
          };
          return c.json(response);
        }

        // Success response
        const response: VoiceToReportResponse = {
          success: true,
          transcription: transcriptionResult.transcription,
          generatedReport: analysisResult.generatedReport,
          processingTime: Date.now() - startTime,
        };

        console.log(`[AiRoutes] Voice-to-report successful - Total processing time: ${response.processingTime}ms`);
        
        return c.json(response);

      } catch (error: any) {
        console.error('[AiRoutes] Error in voice-to-report endpoint:', error);
        return c.json({
          success: false,
          error: 'Internal server error during voice-to-report processing',
          processingTime: Date.now() - Date.now(),
        } as VoiceToReportResponse, 500);
      }
    }
  );

  /**
   * GET /ai/config
   * Get AI configuration for frontend
   */
  router.get('/config', async (c) => {
    try {
      const config = {
        maxAudioDuration: 60, // seconds
        supportedLanguages: ['en', 'fa'],
        supportedAudioFormats: AudioTranscriptionService.getSupportedFormats(),
        recommendedAudioFormat: AudioTranscriptionService.getBestSupportedFormat(),
        features: {
          transcription: transcriptionService.isAvailable(),
          aiAnalysis: aiService.isAvailable(),
          voiceToReport: transcriptionService.isAvailable() && aiService.isAvailable(),
        },
      };

      return c.json({
        success: true,
        config,
      });
    } catch (error: any) {
      console.error('[AiRoutes] Error getting AI config:', error);
      return c.json({
        success: false,
        error: 'Failed to get AI configuration',
      }, 500);
    }
  });

  return router;
}
