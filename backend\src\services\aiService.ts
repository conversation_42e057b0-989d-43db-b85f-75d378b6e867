import { GoogleGenAI, Type } from '@google/genai';
import {
  AiAnalysisRequest,
  AiAnalysisResponse,
  AiGeneratedReport
} from '../types/schemas/aiSchemas';

/**
 * AI Service for processing bug reports using Google Gemini 2.5 Flash Preview (05-20)
 * Handles transcription analysis and structured report generation using the latest Google Gen AI SDK
 */
export class AiService {
  private genAI: GoogleGenAI | null;

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY || '';

    if (!apiKey) {
      console.warn('[AiService] Gemini API key not found. AI features will be disabled.');
      this.genAI = null;
    } else {
      this.genAI = new GoogleGenAI({ apiKey });
    }
  }

  /**
   * Check if AI service is available
   */
  isAvailable(): boolean {
    return !!this.genAI;
  }

  /**
   * Analyze transcribed text and generate structured bug report
   */
  async analyzeTranscription(request: AiAnalysisRequest): Promise<AiAnalysisResponse> {
    const startTime = Date.now();

    try {
      if (!this.isAvailable()) {
        return {
          success: false,
          error: 'AI service is not available. Please check configuration.',
          processingTime: Date.now() - startTime,
        };
      }

      const prompt = this.buildAnalysisPrompt(request);

      // Use the new Google Gen AI SDK with structured output
      const config = {
        temperature: 0.1,
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            title: {
              type: Type.STRING,
              description: 'Brief, clear title for the bug report (max 200 chars)'
            },
            description: {
              type: Type.STRING,
              description: 'Detailed description of the issue (max 2000 chars)'
            },
            stepsToReproduce: {
              type: Type.STRING,
              description: 'Step-by-step reproduction steps (optional, max 2000 chars)'
            },
            expectedBehavior: {
              type: Type.STRING,
              description: 'What should have happened (optional, max 1000 chars)'
            },
            actualBehavior: {
              type: Type.STRING,
              description: 'What actually happened (optional, max 1000 chars)'
            },
            additionalNotes: {
              type: Type.STRING,
              description: 'Any additional context or notes (optional, max 1000 chars)'
            },
            suggestedSeverity: {
              type: Type.STRING,
              enum: ['low', 'medium', 'high', 'critical'],
              description: 'Suggested severity level based on impact'
            },
            suggestedType: {
              type: Type.STRING,
              enum: ['bug', 'feature-request', 'performance', 'ui-ux', 'improvement', 'question', 'other'],
              description: 'Suggested report type based on content'
            },
            suggestedTags: {
              type: Type.ARRAY,
              items: {
                type: Type.STRING
              },
              description: 'Relevant tags for categorization (max 10 tags)'
            },
            confidence: {
              type: Type.NUMBER,
              description: 'Confidence score between 0.0 and 1.0'
            }
          },
          required: ['title', 'description', 'suggestedSeverity', 'suggestedType', 'confidence']
        }
      };

      const contents = [
        {
          role: 'user',
          parts: [
            {
              text: prompt
            }
          ]
        }
      ];

      const result = await this.genAI!.models.generateContent({
        model: 'gemini-2.5-flash-preview-05-20',
        config,
        contents,
      });

      const content = result.text;

      if (!content) {
        return {
          success: false,
          error: 'No response generated from AI model',
          originalTranscription: request.transcription,
          processingTime: Date.now() - startTime,
        };
      }

      const generatedReport = this.parseGeminiResponse(content);

      return {
        success: true,
        generatedReport,
        originalTranscription: request.transcription,
        processingTime: Date.now() - startTime,
      };

    } catch (error: any) {
      console.error('[AiService] Error analyzing transcription:', error);
      return {
        success: false,
        error: `Analysis failed: ${error.message}`,
        originalTranscription: request.transcription,
        processingTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Build the analysis prompt for Gemini
   */
  private buildAnalysisPrompt(request: AiAnalysisRequest): string {
    const { transcription, language, userContext } = request;

    const contextInfo = userContext ? `

User Context:
- Current Page: ${userContext.currentPage || 'Unknown'}
- Browser: ${userContext.userAgent || 'Unknown'}
- Viewport: ${userContext.viewport ? `${userContext.viewport.width}x${userContext.viewport.height}` : 'Unknown'}` : '';

    return `You are an expert bug report analyzer for MUNygo, a peer-to-peer currency exchange platform.

Analyze this user voice transcription and generate a structured bug report:

User Transcription (${language}): "${transcription}"${contextInfo}

Guidelines:
- Extract clear, actionable information from the transcription
- Include specific UI elements mentioned in the description
- Infer severity: critical=app unusable, high=major feature broken, medium=minor issue, low=cosmetic
- Choose appropriate type based on user description
- Add relevant tags for developer categorization
- Be conservative with confidence if transcription is unclear
- Use professional, technical but clear language

The response will be automatically structured according to the defined schema.`;
  }



  /**
   * Parse Gemini response and validate the generated report
   */
  private parseGeminiResponse(content: string): AiGeneratedReport {
    try {
      // With structured output, the response should already be valid JSON
      const parsed = JSON.parse(content);

      // Validate and sanitize the parsed response (extra safety)
      const report: AiGeneratedReport = {
        title: this.sanitizeString(parsed.title || 'AI-Generated Bug Report', 200),
        description: this.sanitizeString(parsed.description || 'No description provided', 2000),
        stepsToReproduce: parsed.stepsToReproduce ? this.sanitizeString(parsed.stepsToReproduce, 2000) : undefined,
        expectedBehavior: parsed.expectedBehavior ? this.sanitizeString(parsed.expectedBehavior, 1000) : undefined,
        actualBehavior: parsed.actualBehavior ? this.sanitizeString(parsed.actualBehavior, 1000) : undefined,
        additionalNotes: parsed.additionalNotes ? this.sanitizeString(parsed.additionalNotes, 1000) : undefined,
        suggestedSeverity: this.validateSeverity(parsed.suggestedSeverity),
        suggestedType: this.validateType(parsed.suggestedType),
        suggestedTags: this.validateTags(parsed.suggestedTags),
        confidence: this.validateConfidence(parsed.confidence),
      };

      return report;

    } catch (error: any) {
      console.error('[AiService] Failed to parse Gemini response:', error);
      console.error('[AiService] Raw response content:', content);

      // Return a fallback report
      return {
        title: 'AI Analysis Failed',
        description: 'The AI service was unable to properly analyze the voice input. Please fill out the form manually.',
        suggestedSeverity: 'medium',
        suggestedType: 'other',
        confidence: 0.1,
      };
    }
  }

  /**
   * Utility methods for validation and sanitization
   */
  private sanitizeString(str: string, maxLength: number): string {
    return str.trim().substring(0, maxLength);
  }

  private validateSeverity(severity: any): 'low' | 'medium' | 'high' | 'critical' {
    const validSeverities = ['low', 'medium', 'high', 'critical'];
    return validSeverities.includes(severity) ? severity : 'medium';
  }

  private validateType(type: any): 'bug' | 'feature-request' | 'performance' | 'ui-ux' | 'improvement' | 'question' | 'other' {
    const validTypes = ['bug', 'feature-request', 'performance', 'ui-ux', 'improvement', 'question', 'other'];
    return validTypes.includes(type) ? type : 'bug';
  }

  private validateTags(tags: any): string[] | undefined {
    if (!Array.isArray(tags)) return undefined;
    return tags
      .filter(tag => typeof tag === 'string' && tag.length <= 50)
      .slice(0, 10);
  }

  private validateConfidence(confidence: any): number {
    const num = parseFloat(confidence);
    if (isNaN(num)) return 0.5;
    return Math.max(0, Math.min(1, num));
  }
}
