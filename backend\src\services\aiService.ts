import { 
  AiAnalysisRequest, 
  AiAnalysisResponse, 
  AiGeneratedReport 
} from '../types/schemas/aiSchemas';

/**
 * AI Service for processing bug reports using Google Gemini
 * Handles transcription analysis and structured report generation
 */
export class AiService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.GEMINI_API_KEY || '';
    this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent';
    
    if (!this.apiKey) {
      console.warn('[AiService] Gemini API key not found. AI features will be disabled.');
    }
  }

  /**
   * Check if AI service is available
   */
  isAvailable(): boolean {
    return !!this.apiKey;
  }

  /**
   * Analyze transcribed text and generate structured bug report
   */
  async analyzeTranscription(request: AiAnalysisRequest): Promise<AiAnalysisResponse> {
    const startTime = Date.now();

    try {
      if (!this.isAvailable()) {
        return {
          success: false,
          error: 'AI service is not available. Please check configuration.',
          processingTime: Date.now() - startTime,
        };
      }

      const prompt = this.buildAnalysisPrompt(request);
      const geminiResponse = await this.callGeminiApi(prompt);
      
      if (!geminiResponse.success) {
        return {
          success: false,
          error: geminiResponse.error,
          originalTranscription: request.transcription,
          processingTime: Date.now() - startTime,
        };
      }

      const generatedReport = this.parseGeminiResponse(geminiResponse.content);
      
      return {
        success: true,
        generatedReport,
        originalTranscription: request.transcription,
        processingTime: Date.now() - startTime,
      };

    } catch (error: any) {
      console.error('[AiService] Error analyzing transcription:', error);
      return {
        success: false,
        error: `Analysis failed: ${error.message}`,
        originalTranscription: request.transcription,
        processingTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Build the analysis prompt for Gemini
   */
  private buildAnalysisPrompt(request: AiAnalysisRequest): string {
    const { transcription, language, userContext } = request;
    
    const contextInfo = userContext ? `
User Context:
- Current Page: ${userContext.currentPage || 'Unknown'}
- Browser: ${userContext.userAgent || 'Unknown'}
- Viewport: ${userContext.viewport ? `${userContext.viewport.width}x${userContext.viewport.height}` : 'Unknown'}
` : '';

    return `You are an expert bug report analyzer for a web application called MUNygo (a peer-to-peer currency exchange platform). 

Your task is to analyze the following user voice transcription and generate a structured bug report. The user is describing an issue they encountered while using the application.

User Transcription (Language: ${language}):
"${transcription}"
${contextInfo}

Please analyze this transcription and generate a structured bug report in JSON format with the following fields:

{
  "title": "Brief, clear title (max 200 chars)",
  "description": "Detailed description of the issue (max 2000 chars)",
  "stepsToReproduce": "Step-by-step reproduction steps if mentioned (max 2000 chars, optional)",
  "expectedBehavior": "What should have happened (max 1000 chars, optional)",
  "actualBehavior": "What actually happened (max 1000 chars, optional)",
  "additionalNotes": "Any additional context or notes (max 1000 chars, optional)",
  "suggestedSeverity": "low|medium|high|critical",
  "suggestedType": "bug|feature-request|performance|ui-ux|improvement|question|other",
  "suggestedTags": ["tag1", "tag2"] (max 10 tags, optional),
  "confidence": 0.0-1.0 (how confident you are in this analysis)
}

Guidelines:
1. Extract clear, actionable information from the transcription
2. If the user mentions specific UI elements, include them in the description
3. Infer severity based on impact described (critical=app unusable, high=major feature broken, medium=minor feature issue, low=cosmetic)
4. Choose the most appropriate type based on what the user is describing
5. Add relevant tags that would help developers categorize the issue
6. Be conservative with confidence - lower if transcription is unclear
7. If information is missing or unclear, don't make assumptions - leave fields empty or mark as optional
8. Keep language professional and technical but clear

Respond ONLY with the JSON object, no additional text or formatting.`;
  }

  /**
   * Call Gemini API with the analysis prompt
   */
  private async callGeminiApi(prompt: string): Promise<{ success: boolean; content?: string; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0.1, // Low temperature for consistent, factual responses
            topK: 1,
            topP: 0.8,
            maxOutputTokens: 2048,
          },
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Gemini API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      
      if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
        throw new Error('Invalid response format from Gemini API');
      }

      const content = data.candidates[0].content.parts[0].text;
      return { success: true, content };

    } catch (error: any) {
      console.error('[AiService] Gemini API call failed:', error);
      return { 
        success: false, 
        error: `Gemini API call failed: ${error.message}` 
      };
    }
  }

  /**
   * Parse Gemini response and validate the generated report
   */
  private parseGeminiResponse(content: string): AiGeneratedReport {
    try {
      // Clean the response - remove any markdown formatting
      const cleanContent = content.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      
      const parsed = JSON.parse(cleanContent);
      
      // Validate and sanitize the parsed response
      const report: AiGeneratedReport = {
        title: this.sanitizeString(parsed.title || 'AI-Generated Bug Report', 200),
        description: this.sanitizeString(parsed.description || 'No description provided', 2000),
        stepsToReproduce: parsed.stepsToReproduce ? this.sanitizeString(parsed.stepsToReproduce, 2000) : undefined,
        expectedBehavior: parsed.expectedBehavior ? this.sanitizeString(parsed.expectedBehavior, 1000) : undefined,
        actualBehavior: parsed.actualBehavior ? this.sanitizeString(parsed.actualBehavior, 1000) : undefined,
        additionalNotes: parsed.additionalNotes ? this.sanitizeString(parsed.additionalNotes, 1000) : undefined,
        suggestedSeverity: this.validateSeverity(parsed.suggestedSeverity),
        suggestedType: this.validateType(parsed.suggestedType),
        suggestedTags: this.validateTags(parsed.suggestedTags),
        confidence: this.validateConfidence(parsed.confidence),
      };

      return report;

    } catch (error: any) {
      console.error('[AiService] Failed to parse Gemini response:', error);
      
      // Return a fallback report
      return {
        title: 'AI Analysis Failed',
        description: 'The AI service was unable to properly analyze the voice input. Please fill out the form manually.',
        suggestedSeverity: 'medium',
        suggestedType: 'other',
        confidence: 0.1,
      };
    }
  }

  /**
   * Utility methods for validation and sanitization
   */
  private sanitizeString(str: string, maxLength: number): string {
    return str.trim().substring(0, maxLength);
  }

  private validateSeverity(severity: any): 'low' | 'medium' | 'high' | 'critical' {
    const validSeverities = ['low', 'medium', 'high', 'critical'];
    return validSeverities.includes(severity) ? severity : 'medium';
  }

  private validateType(type: any): 'bug' | 'feature-request' | 'performance' | 'ui-ux' | 'improvement' | 'question' | 'other' {
    const validTypes = ['bug', 'feature-request', 'performance', 'ui-ux', 'improvement', 'question', 'other'];
    return validTypes.includes(type) ? type : 'bug';
  }

  private validateTags(tags: any): string[] | undefined {
    if (!Array.isArray(tags)) return undefined;
    return tags
      .filter(tag => typeof tag === 'string' && tag.length <= 50)
      .slice(0, 10);
  }

  private validateConfidence(confidence: any): number {
    const num = parseFloat(confidence);
    if (isNaN(num)) return 0.5;
    return Math.max(0, Math.min(1, num));
  }
}
