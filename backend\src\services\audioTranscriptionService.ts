import { 
  AudioTranscriptionRequest, 
  AudioTranscriptionResponse 
} from '../types/schemas/aiSchemas';

/**
 * Audio Transcription Service using Google Speech-to-Text API
 * Handles audio file processing and speech recognition
 */
export class AudioTranscriptionService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.GOOGLE_SPEECH_API_KEY || process.env.GEMINI_API_KEY || '';
    this.baseUrl = 'https://speech.googleapis.com/v1/speech:recognize';
    
    if (!this.apiKey) {
      console.warn('[AudioTranscriptionService] Google Speech API key not found. Transcription features will be disabled.');
    }
  }

  /**
   * Check if transcription service is available
   */
  isAvailable(): boolean {
    return !!this.apiKey;
  }

  /**
   * Transcribe audio data to text
   */
  async transcribeAudio(request: AudioTranscriptionRequest): Promise<AudioTranscriptionResponse> {
    try {
      if (!this.isAvailable()) {
        return {
          success: false,
          error: 'Transcription service is not available. Please check configuration.',
        };
      }

      // Validate audio data
      if (!this.isValidAudioData(request.audioData)) {
        return {
          success: false,
          error: 'Invalid audio data format.',
        };
      }

      // Check duration limits (max 1 minute)
      if (request.duration > 60) {
        return {
          success: false,
          error: 'Audio duration exceeds maximum limit of 60 seconds.',
        };
      }

      const speechResponse = await this.callSpeechApi(request);
      
      if (!speechResponse.success) {
        return {
          success: false,
          error: speechResponse.error,
        };
      }

      return {
        success: true,
        transcription: speechResponse.transcription,
        confidence: speechResponse.confidence,
        language: speechResponse.language || request.language,
      };

    } catch (error: any) {
      console.error('[AudioTranscriptionService] Error transcribing audio:', error);
      return {
        success: false,
        error: `Transcription failed: ${error.message}`,
      };
    }
  }

  /**
   * Validate base64 audio data format
   */
  private isValidAudioData(audioData: string): boolean {
    try {
      // Check if it's valid base64
      const decoded = Buffer.from(audioData, 'base64');
      return decoded.length > 0;
    } catch {
      return false;
    }
  }

  /**
   * Call Google Speech-to-Text API
   */
  private async callSpeechApi(request: AudioTranscriptionRequest): Promise<{
    success: boolean;
    transcription?: string;
    confidence?: number;
    language?: string;
    error?: string;
  }> {
    try {
      // Map language codes
      const languageCode = this.mapLanguageCode(request.language || 'en');
      
      // Determine audio encoding from MIME type
      const encoding = this.getAudioEncoding(request.mimeType);
      
      const requestBody = {
        config: {
          encoding,
          sampleRateHertz: 16000, // Standard sample rate for web audio
          languageCode,
          enableAutomaticPunctuation: true,
          enableWordTimeOffsets: false,
          model: 'latest_short', // Optimized for short audio clips
        },
        audio: {
          content: request.audioData,
        },
      };

      const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Speech API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      
      if (!data.results || data.results.length === 0) {
        return {
          success: false,
          error: 'No speech detected in the audio. Please try speaking more clearly or check your microphone.',
        };
      }

      const result = data.results[0];
      const alternative = result.alternatives[0];
      
      if (!alternative || !alternative.transcript) {
        return {
          success: false,
          error: 'Unable to transcribe the audio. Please try again.',
        };
      }

      return {
        success: true,
        transcription: alternative.transcript.trim(),
        confidence: alternative.confidence || 0.5,
        language: languageCode,
      };

    } catch (error: any) {
      console.error('[AudioTranscriptionService] Speech API call failed:', error);
      return {
        success: false,
        error: `Speech API call failed: ${error.message}`,
      };
    }
  }

  /**
   * Map internal language codes to Google Speech API language codes
   */
  private mapLanguageCode(language: string): string {
    const languageMap: Record<string, string> = {
      'en': 'en-US',
      'fa': 'fa-IR',
    };
    
    return languageMap[language] || 'en-US';
  }

  /**
   * Determine audio encoding from MIME type
   */
  private getAudioEncoding(mimeType: string): string {
    const encodingMap: Record<string, string> = {
      'audio/webm': 'WEBM_OPUS',
      'audio/webm;codecs=opus': 'WEBM_OPUS',
      'audio/ogg': 'OGG_OPUS',
      'audio/ogg;codecs=opus': 'OGG_OPUS',
      'audio/wav': 'LINEAR16',
      'audio/mp3': 'MP3',
      'audio/mpeg': 'MP3',
      'audio/mp4': 'MP3',
      'audio/m4a': 'MP3',
    };
    
    return encodingMap[mimeType.toLowerCase()] || 'WEBM_OPUS';
  }

  /**
   * Get supported audio formats for the client
   */
  static getSupportedFormats(): string[] {
    return [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/ogg;codecs=opus',
      'audio/ogg',
      'audio/wav',
      'audio/mp3',
      'audio/mpeg',
    ];
  }

  /**
   * Get the best supported audio format for the current browser
   */
  static getBestSupportedFormat(): string {
    // This will be used by the frontend to determine the best format
    // Priority: WebM Opus > OGG Opus > WAV
    const formats = AudioTranscriptionService.getSupportedFormats();
    return formats[0]; // Default to WebM Opus
  }
}
