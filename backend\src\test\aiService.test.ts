import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AiService } from '../services/aiService';
import type { AiAnalysisRequest } from '../types/schemas/aiSchemas';

// Mock the Google Gen AI SDK
vi.mock('@google/genai', () => ({
  GoogleGenAI: vi.fn().mockImplementation(() => ({
    models: {
      generateContent: vi.fn()
    }
  }))
}));

describe('AiService', () => {
  let aiService: AiService;
  let mockGenerateContent: any;

  beforeEach(() => {
    // Reset environment variables
    process.env.GEMINI_API_KEY = 'test-api-key';
    
    // Create new instance
    aiService = new AiService();
    
    // Get the mock function
    const { GoogleGenAI } = require('@google/genai');
    const mockInstance = new GoogleGenAI();
    mockGenerateContent = mockInstance.models.generateContent;
  });

  describe('isAvailable', () => {
    it('should return true when API key is provided', () => {
      expect(aiService.isAvailable()).toBe(true);
    });

    it('should return false when API key is not provided', () => {
      delete process.env.GEMINI_API_KEY;
      const serviceWithoutKey = new AiService();
      expect(serviceWithoutKey.isAvailable()).toBe(false);
    });
  });

  describe('analyzeTranscription', () => {
    const mockRequest: AiAnalysisRequest = {
      transcription: 'The login button is not working when I click it',
      language: 'en',
      userContext: {
        currentPage: 'https://example.com/login',
        userAgent: 'Mozilla/5.0...',
        viewport: { width: 1920, height: 1080 }
      }
    };

    it('should return error when service is not available', async () => {
      delete process.env.GEMINI_API_KEY;
      const serviceWithoutKey = new AiService();
      
      const result = await serviceWithoutKey.analyzeTranscription(mockRequest);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('AI service is not available');
    });

    it('should successfully analyze transcription and return structured report', async () => {
      const mockAiResponse = {
        text: JSON.stringify({
          title: 'Login Button Not Working',
          description: 'User reports that the login button is unresponsive when clicked',
          stepsToReproduce: '1. Navigate to login page\n2. Click login button\n3. Nothing happens',
          expectedBehavior: 'Login form should submit and user should be authenticated',
          actualBehavior: 'Button click has no effect',
          suggestedSeverity: 'high',
          suggestedType: 'bug',
          suggestedTags: ['login', 'ui', 'button'],
          confidence: 0.85
        })
      };

      mockGenerateContent.mockResolvedValue(mockAiResponse);

      const result = await aiService.analyzeTranscription(mockRequest);

      expect(result.success).toBe(true);
      expect(result.generatedReport).toBeDefined();
      expect(result.generatedReport?.title).toBe('Login Button Not Working');
      expect(result.generatedReport?.suggestedType).toBe('bug');
      expect(result.generatedReport?.suggestedSeverity).toBe('high');
      expect(result.generatedReport?.confidence).toBe(0.85);
      expect(result.originalTranscription).toBe(mockRequest.transcription);
      expect(result.processingTime).toBeGreaterThan(0);
    });

    it('should handle malformed AI response gracefully', async () => {
      const mockAiResponse = {
        text: 'invalid json response'
      };

      mockGenerateContent.mockResolvedValue(mockAiResponse);

      const result = await aiService.analyzeTranscription(mockRequest);

      expect(result.success).toBe(true);
      expect(result.generatedReport).toBeDefined();
      expect(result.generatedReport?.title).toBe('AI Analysis Failed');
      expect(result.generatedReport?.confidence).toBe(0.1);
    });

    it('should handle API errors gracefully', async () => {
      mockGenerateContent.mockRejectedValue(new Error('API Error'));

      const result = await aiService.analyzeTranscription(mockRequest);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Analysis failed: API Error');
      expect(result.originalTranscription).toBe(mockRequest.transcription);
    });

    it('should handle empty AI response', async () => {
      const mockAiResponse = {
        text: ''
      };

      mockGenerateContent.mockResolvedValue(mockAiResponse);

      const result = await aiService.analyzeTranscription(mockRequest);

      expect(result.success).toBe(false);
      expect(result.error).toBe('No response generated from AI model');
    });

    it('should validate and sanitize AI response fields', async () => {
      const mockAiResponse = {
        text: JSON.stringify({
          title: 'A'.repeat(300), // Too long title
          description: 'Valid description',
          suggestedSeverity: 'invalid-severity',
          suggestedType: 'invalid-type',
          suggestedTags: ['tag1', 'tag2', 'A'.repeat(100)], // One tag too long
          confidence: 1.5 // Invalid confidence
        })
      };

      mockGenerateContent.mockResolvedValue(mockAiResponse);

      const result = await aiService.analyzeTranscription(mockRequest);

      expect(result.success).toBe(true);
      expect(result.generatedReport?.title).toHaveLength(200); // Truncated
      expect(result.generatedReport?.suggestedSeverity).toBe('medium'); // Default
      expect(result.generatedReport?.suggestedType).toBe('bug'); // Default
      expect(result.generatedReport?.suggestedTags).toHaveLength(2); // Long tag filtered out
      expect(result.generatedReport?.confidence).toBe(1); // Clamped to max
    });
  });

  describe('buildAnalysisPrompt', () => {
    it('should build comprehensive prompt with context', () => {
      const request: AiAnalysisRequest = {
        transcription: 'Test transcription',
        language: 'en',
        userContext: {
          currentPage: 'https://example.com/test',
          userAgent: 'Mozilla/5.0...',
          viewport: { width: 1920, height: 1080 }
        }
      };

      // Access private method for testing (TypeScript hack)
      const prompt = (aiService as any).buildAnalysisPrompt(request);

      expect(prompt).toContain('Test transcription');
      expect(prompt).toContain('Language: en');
      expect(prompt).toContain('https://example.com/test');
      expect(prompt).toContain('1920x1080');
      expect(prompt).toContain('MUNygo');
      expect(prompt).toContain('JSON format');
    });

    it('should handle missing user context', () => {
      const request: AiAnalysisRequest = {
        transcription: 'Test transcription',
        language: 'fa'
      };

      const prompt = (aiService as any).buildAnalysisPrompt(request);

      expect(prompt).toContain('Test transcription');
      expect(prompt).toContain('Language: fa');
      expect(prompt).not.toContain('Current Page:');
    });
  });
});
