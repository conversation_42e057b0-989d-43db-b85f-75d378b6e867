import { z } from 'zod';

/**
 * Zod schemas for AI-powered bug reporting features
 */

// Audio transcription request schema
export const AudioTranscriptionRequestSchema = z.object({
  audioData: z.string().min(1, 'Audio data is required'), // Base64 encoded audio
  mimeType: z.string().min(1, 'MIME type is required'),
  duration: z.number().positive('Duration must be positive'),
  language: z.enum(['en', 'fa']).optional().default('en'),
});

// Audio transcription response schema
export const AudioTranscriptionResponseSchema = z.object({
  success: z.boolean(),
  transcription: z.string().optional(),
  confidence: z.number().min(0).max(1).optional(),
  language: z.string().optional(),
  error: z.string().optional(),
});

// AI analysis request schema
export const AiAnalysisRequestSchema = z.object({
  transcription: z.string().min(1, 'Transcription is required'),
  language: z.enum(['en', 'fa']).optional().default('en'),
  userContext: z.object({
    currentPage: z.string().optional(),
    userAgent: z.string().optional(),
    viewport: z.object({
      width: z.number(),
      height: z.number(),
    }).optional(),
  }).optional(),
});

// AI-generated bug report structure
export const AiGeneratedReportSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().min(1).max(2000),
  stepsToReproduce: z.string().max(2000).optional(),
  expectedBehavior: z.string().max(1000).optional(),
  actualBehavior: z.string().max(1000).optional(),
  additionalNotes: z.string().max(1000).optional(),
  suggestedSeverity: z.enum(['low', 'medium', 'high', 'critical']),
  suggestedType: z.enum(['bug', 'feature-request', 'performance', 'ui-ux', 'improvement', 'question', 'other']),
  suggestedTags: z.array(z.string().max(50)).max(10).optional(),
  confidence: z.number().min(0).max(1),
});

// AI analysis response schema
export const AiAnalysisResponseSchema = z.object({
  success: z.boolean(),
  generatedReport: AiGeneratedReportSchema.optional(),
  originalTranscription: z.string().optional(),
  processingTime: z.number().optional(),
  error: z.string().optional(),
});

// Combined voice-to-report request schema
export const VoiceToReportRequestSchema = z.object({
  audioData: z.string().min(1, 'Audio data is required'),
  mimeType: z.string().min(1, 'MIME type is required'),
  duration: z.number().positive('Duration must be positive'),
  language: z.enum(['en', 'fa']).optional().default('en'),
  userContext: z.object({
    currentPage: z.string().optional(),
    userAgent: z.string().optional(),
    viewport: z.object({
      width: z.number(),
      height: z.number(),
    }).optional(),
  }).optional(),
});

// Combined voice-to-report response schema
export const VoiceToReportResponseSchema = z.object({
  success: z.boolean(),
  transcription: z.string().optional(),
  generatedReport: AiGeneratedReportSchema.optional(),
  processingTime: z.number().optional(),
  error: z.string().optional(),
});

// Type exports for TypeScript
export type AudioTranscriptionRequest = z.infer<typeof AudioTranscriptionRequestSchema>;
export type AudioTranscriptionResponse = z.infer<typeof AudioTranscriptionResponseSchema>;
export type AiAnalysisRequest = z.infer<typeof AiAnalysisRequestSchema>;
export type AiAnalysisResponse = z.infer<typeof AiAnalysisResponseSchema>;
export type AiGeneratedReport = z.infer<typeof AiGeneratedReportSchema>;
export type VoiceToReportRequest = z.infer<typeof VoiceToReportRequestSchema>;
export type VoiceToReportResponse = z.infer<typeof VoiceToReportResponseSchema>;
