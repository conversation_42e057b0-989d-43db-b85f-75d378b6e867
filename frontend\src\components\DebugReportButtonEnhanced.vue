<template>
  <div>
    <!-- Debug Report Button -->
    <n-button
      type="info"
      size="small"
      @click="handleModalOpen"
      :disabled="isLoading"
      v-if="isDevelopment"
    >
      <template #icon>
        <n-icon><BugIcon /></n-icon>
      </template>
      {{ t('debug.reportIssue') }}
    </n-button>

    <!-- Enhanced Report Modal -->
    <n-modal v-model:show="showModal" preset="dialog" style="width: 90%; max-width: 800px;">
      <template #header>
        <div class="flex items-center gap-2">
          <n-icon size="20"><BugIcon /></n-icon>
          <span>{{ t('debug.reportIssue') }}</span>
        </div>
      </template>

      <div class="space-y-6">
        <!-- Report Type Selection with Visual Tags -->
        <div>
          <label class="block text-sm font-medium mb-2">{{ t('debug.reportType') }}</label>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div
              v-for="type in reportTypes"
              :key="type.value"
              @click="handleTypeChange(type.value)"
              :class="[
                'p-4 border-2 rounded-lg cursor-pointer transition-all',
                reportForm.type === type.value
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 hover:border-gray-300 dark:border-gray-700 dark:hover:border-gray-600'
              ]"
            >
              <div class="flex items-center gap-3">
                <n-icon :size="20" :color="type.color">
                  <component :is="getTypeIcon(type.icon)" />
                </n-icon>
                <div class="flex-1">
                  <div class="font-medium">{{ type.label }}</div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">{{ type.description }}</div>                  <div class="flex gap-1 mt-2">
                    <n-tag
                      v-for="tag in type.tags"
                      :key="tag"
                      size="small"
                      :type="getTagType(tag)"
                      :class="[
                        'cursor-pointer transition-all hover:scale-105',
                        reportForm.reportTags?.includes(tag) 
                          ? 'ring-2 ring-blue-400 ring-opacity-50' 
                          : 'hover:opacity-80'
                      ]"
                      @click.stop="toggleTag(tag)"
                      :title="reportForm.reportTags?.includes(tag) 
                        ? t('debug.tags.clickToRemove') 
                        : t('debug.tags.clickToAdd')"
                    >
                      {{ tag }}
                      <n-icon 
                        v-if="reportForm.reportTags?.includes(tag)" 
                        size="12" 
                        class="ml-1"
                      >
                        <component :is="CheckIcon" />
                      </n-icon>
                    </n-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Severity Level -->
        <div>
          <label class="block text-sm font-medium mb-2">{{ t('debug.severityText') }}</label>
          <n-select
            v-model:value="reportForm.severity"
            :options="severityOptions"
            :placeholder="t('debug.selectSeverity')"
            @update:value="handleFormInput"
          />
        </div>        <!-- Custom Tags -->
        <div>
          <label class="block text-sm font-medium mb-2">{{ t('debug.tags.addCustom') }}</label>
          <n-dynamic-tags v-model:value="reportForm.reportTags" />
          <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {{ t('debug.tagsDescription') }}
          </div>
          <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">
            💡 {{ t('debug.tags.clickHint') }}
          </div>
        </div>

        <!-- AI Voice Recording Section -->
        <div class="ai-voice-section">
          <div class="flex items-center justify-between mb-3">
            <label class="block text-sm font-medium">{{ t('voice.recordBugReport') }}</label>
            <n-tag v-if="aiAnalysis.isAvailable" type="success" size="small">
              {{ t('voice.aiEnabled') }}
            </n-tag>
            <n-tag v-else type="warning" size="small">
              {{ t('voice.aiDisabled') }}
            </n-tag>
          </div>

          <div class="voice-recorder-container">
            <VoiceRecorder
              ref="voiceRecorderRef"
              :language="currentLanguage"
              :auto-process="true"
              @transcription-complete="handleTranscriptionComplete"
              @analysis-complete="handleAnalysisComplete"
              @processing-error="handleVoiceError"
            />
          </div>

          <!-- AI Analysis Results -->
          <div v-if="aiAnalysis.hasTranscription || aiAnalysis.hasGeneratedReport" class="ai-results mt-4">
            <!-- Transcription Display -->
            <div v-if="aiAnalysis.hasTranscription" class="transcription-result mb-3">
              <label class="block text-sm font-medium mb-2">{{ t('voice.transcription') }}</label>
              <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border">
                <p class="text-sm">{{ aiAnalysis.transcription }}</p>
              </div>
            </div>

            <!-- AI Generated Report Preview -->
            <div v-if="aiAnalysis.hasGeneratedReport" class="ai-report-preview">
              <div class="flex items-center justify-between mb-2">
                <label class="block text-sm font-medium">{{ t('voice.aiGeneratedReport') }}</label>
                <div class="flex items-center gap-2">
                  <n-tag type="info" size="small">
                    {{ t('voice.confidence', { confidence: aiAnalysis.confidencePercentage }) }}
                  </n-tag>
                  <n-button size="tiny" type="primary" @click="applyAiReport">
                    {{ t('voice.applyToForm') }}
                  </n-button>
                </div>
              </div>

              <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
                <div class="space-y-2 text-sm">
                  <div><strong>{{ t('debug.title') }}:</strong> {{ aiAnalysis.generatedReport.value?.title }}</div>
                  <div><strong>{{ t('debug.reportType') }}:</strong> {{ getTypeLabel(aiAnalysis.generatedReport.value?.type) }}</div>
                  <div><strong>{{ t('debug.severityText') }}:</strong> {{ getSeverityLabel(aiAnalysis.generatedReport.value?.severity) }}</div>
                  <div><strong>{{ t('debug.description') }}:</strong> {{ aiAnalysis.generatedReport.value?.description }}</div>
                  <div v-if="aiAnalysis.generatedReport.value?.reportTags?.length" class="flex gap-1 flex-wrap">
                    <strong>{{ t('debug.tagsText') }}:</strong>
                    <n-tag v-for="tag in aiAnalysis.generatedReport.value.reportTags" :key="tag" size="small">
                      {{ tag }}
                    </n-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Auto-save notification -->
        <div v-if="formDrafts.hasDrafts || formDrafts.lastAutoSaveAt" class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
          <div class="flex items-center gap-2 text-blue-700 dark:text-blue-300">
            <n-icon size="16"><InfoIcon /></n-icon>
            <span class="text-sm">{{ t('debug.autoSaveEnabled') }}</span>
          </div>
          <div v-if="formDrafts.lastAutoSaveAt.value" class="text-xs text-blue-600 dark:text-blue-400 mt-1">
            Last saved: {{ formDrafts.lastAutoSaveAt.value ? formatTime(formDrafts.lastAutoSaveAt.value) : '' }}
          </div>
        </div>

        <!-- Title -->
        <div>
          <label class="block text-sm font-medium mb-2">{{ t('debug.title') }}</label>
          <n-input
            v-model:value="reportForm.title"
            :placeholder="t('debug.titlePlaceholder')"
            clearable
            @input="handleFormInput"
          />
        </div>

        <!-- Description -->
        <div>
          <label class="block text-sm font-medium mb-2">{{ t('debug.description') }}</label>
          <n-input
            v-model:value="reportForm.description"
            type="textarea"
            :placeholder="t('debug.descriptionPlaceholder')"
            :rows="4"
            clearable
            @input="handleFormInput"
          />
        </div>

        <!-- Dynamic Fields Based on Report Type -->
        <div v-if="reportForm.type === 'bug'">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium mb-2">{{ t('debug.stepsToReproduce') }}</label>
              <n-input
                v-model:value="reportForm.stepsToReproduce"
                type="textarea"
                :placeholder="t('debug.stepsPlaceholder')"
                :rows="3"
                clearable
                @input="handleFormInput"
              />
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">{{ t('debug.expectedBehavior') }}</label>
              <n-input
                v-model:value="reportForm.expectedBehavior"
                type="textarea"
                :placeholder="t('debug.expectedPlaceholder')"
                :rows="2"
                clearable
                @input="handleFormInput"
              />
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">{{ t('debug.actualBehavior') }}</label>
              <n-input
                v-model:value="reportForm.actualBehavior"
                type="textarea"
                :placeholder="t('debug.actualPlaceholder')"
                :rows="2"
                clearable
                @input="handleFormInput"
              />
            </div>
          </div>
        </div>

        <!-- Context Information Display -->
        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
          <h4 class="font-medium mb-2 flex items-center gap-2">
            <n-icon><InfoIcon /></n-icon>
            {{ t('debug.contextInfo') }}
          </h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>{{ t('debug.currentPage') }}:</strong>
              <div class="text-gray-600 dark:text-gray-400 break-all">{{ currentUrl }}</div>
            </div>
            <div>
              <strong>{{ t('debug.logEntries') }}:</strong>
              <div class="text-gray-600 dark:text-gray-400">{{ logCount }} entries</div>
            </div>
            <div>
              <strong>{{ t('debug.userActions') }}:</strong>
              <div class="text-gray-600 dark:text-gray-400">{{ userActionCount }} recent actions</div>
            </div>
            <div>
              <strong>{{ t('debug.viewport') }}:</strong>
              <div class="text-gray-600 dark:text-gray-400">{{ viewport.width }}x{{ viewport.height }}</div>
            </div>
          </div>

          <!-- Recent User Actions Preview -->
          <div v-if="recentActions.length > 0" class="mt-4">
            <h5 class="font-medium mb-2">{{ t('debug.recentActions') }}:</h5>
            <div class="max-h-32 overflow-y-auto space-y-1">
              <div
                v-for="action in recentActions.slice(0, 5)"
                :key="action.timestamp"
                class="text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded"
              >
                <div class="flex justify-between">
                  <span class="font-medium">{{ action.action }}</span>
                  <span class="text-gray-500">{{ formatTime(action.timestamp) }}</span>
                </div>
                <div v-if="action.details" class="text-gray-600 dark:text-gray-400 mt-1">
                  {{ JSON.stringify(action.details, null, 2).slice(0, 100) }}...
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Additional Notes -->
        <div>
          <label class="block text-sm font-medium mb-2">{{ t('debug.additionalNotes') }}</label>
          <n-input
            v-model:value="reportForm.additionalNotes"
            type="textarea"
            :placeholder="t('debug.additionalNotesPlaceholder')"
            :rows="2"
            clearable
            @input="handleFormInput"
          />
        </div>
      </div>

      <template #action>
        <div class="flex justify-between">
          <div class="flex gap-2">
            <n-button @click="showModal = false" :disabled="isLoading">
              {{ t('common.cancel') }}
            </n-button>
            <n-button
              @click="handleResetForm"
              :disabled="isLoading"
              type="warning"
              ghost
            >
              {{ t('debug.resetForm') }}
            </n-button>
          </div>
          <div class="flex gap-2 items-center">
            <!-- Offline indicator -->
            <div v-if="!connectionStore.isConnected" class="flex items-center gap-1 text-orange-500 text-sm">
              <n-icon size="16"><InfoIcon /></n-icon>
              <span>{{ t('debug.offlineMode') }}</span>
            </div>
            <!-- Pending offline reports indicator -->
            <div v-if="offlineReports.hasOfflineReports" class="flex items-center gap-1 text-blue-500 text-sm">
              <n-icon size="16"><InfoIcon /></n-icon>
              <span>{{ t('debug.offlineReportsCount', { count: offlineReports.offlineReportCount.value }) }}</span>
            </div>
            <n-button
              type="primary"
              @click="submitReport"
              :loading="isLoading"
              :disabled="!isFormValid"
            >
              {{ t('debug.sendReport') }}
            </n-button>
          </div>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useMessage, useDialog } from 'naive-ui';
import { useClientLogger } from '@/composables/useClientLogger';
import { useOfflineReports } from '@/composables/useOfflineReports';
import { useFormDrafts } from '@/composables/useFormDrafts';
import { useConnectionStore } from '@/stores/connection';
import { useAiAnalysis } from '@/composables/useAiAnalysis';
import type { ReportDetails, ReportSeverity, ReportTypeOption } from '@/types/logging';
import VoiceRecorder from '@/components/VoiceRecorder.vue';
import {
  Bug as BugIcon,
  InfoCircle as InfoIcon,
  Bulb as LightbulbIcon,
  Bolt as ZapIcon,
  Palette as PaletteIcon,
  TrendingUp as TrendingUpIcon,
  QuestionMark as HelpCircleIcon,
  Check as CheckIcon
} from '@vicons/tabler';

const { t } = useI18n();
const message = useMessage();
const dialog = useDialog();
const logger = useClientLogger();
const offlineReports = useOfflineReports();
const formDrafts = useFormDrafts();
const connectionStore = useConnectionStore();
const aiAnalysis = useAiAnalysis();

// Component refs
const voiceRecorderRef = ref<InstanceType<typeof VoiceRecorder> | null>(null);

// Component state
const showModal = ref(false);
const isLoading = ref(false);
const currentUrl = ref(window.location.href);

// Development environment check - show in dev OR if explicitly enabled
const isDevelopment = computed(() => {
  const devMode = import.meta.env.DEV;
  const debugEnabled = import.meta.env.VITE_ENABLE_DEBUG_REPORT === 'true';
  console.log('Debug Report Button - DEV:', devMode, 'VITE_ENABLE_DEBUG_REPORT:', import.meta.env.VITE_ENABLE_DEBUG_REPORT, 'Show button:', devMode || debugEnabled);
  return devMode || debugEnabled;
});

// Enhanced Report Types with tags and colors
const reportTypes: ReportTypeOption[] = [
  {
    value: 'bug',
    label: t('debug.reportTypes.bug'),
    description: t('debug.reportTypes.bugDescription'),
    icon: 'bug',
    color: '#ef4444',
    tags: [t('debug.tags.predefined.urgent'), t('debug.tags.predefined.fix-needed'), t('debug.tags.predefined.error')]
  },
  {
    value: 'feature-request',
    label: t('debug.reportTypes.feature'),
    description: t('debug.reportTypes.featureDescription'),
    icon: 'lightbulb',
    color: '#3b82f6',
    tags: [t('debug.tags.predefined.enhancement'), t('debug.tags.predefined.new-feature'), t('debug.tags.predefined.idea')]
  },
  {
    value: 'performance',
    label: t('debug.reportTypes.performance'),
    description: t('debug.reportTypes.performanceDescription'),
    icon: 'zap',
    color: '#f59e0b',
    tags: [t('debug.tags.predefined.slow'), t('debug.tags.predefined.optimization'), t('debug.tags.predefined.speed')]
  },
  {
    value: 'ui-ux',
    label: t('debug.reportTypes.uiux'),
    description: t('debug.reportTypes.uiuxDescription'),
    icon: 'palette',
    color: '#8b5cf6',
    tags: [t('debug.tags.predefined.design'), t('debug.tags.predefined.user-experience'), t('debug.tags.predefined.interface')]
  },
  {
    value: 'improvement',
    label: t('debug.reportTypes.improvement'),
    description: t('debug.reportTypes.improvementDescription'),
    icon: 'trending-up',
    color: '#10b981',
    tags: [t('debug.tags.predefined.enhancement'), t('debug.tags.predefined.better-way'), t('debug.tags.predefined.suggestion')]
  },
  {
    value: 'question',
    label: t('debug.reportTypes.question'),
    description: t('debug.reportTypes.questionDescription'),
    icon: 'help-circle',
    color: '#6b7280',
    tags: [t('debug.tags.predefined.help'), t('debug.tags.predefined.unclear'), t('debug.tags.predefined.documentation')]
  },
  {
    value: 'other',
    label: t('debug.reportTypes.other'),
    description: t('debug.reportTypes.otherDescription'),
    icon: 'more',
    color: '#6b7280',
    tags: [t('debug.tags.predefined.miscellaneous'), t('debug.tags.predefined.general')]
  }
];

// Severity options
const severityOptions = [
  { 
    label: t('debug.severity.low'),
    value: 'low' as ReportSeverity,
    color: '#10b981'
  },
  { 
    label: t('debug.severity.medium'),
    value: 'medium' as ReportSeverity,
    color: '#f59e0b'
  },
  { 
    label: t('debug.severity.high'),
    value: 'high' as ReportSeverity,
    color: '#ef4444'
  },
  { 
    label: t('debug.severity.critical'),
    value: 'critical' as ReportSeverity,
    color: '#dc2626'
  }
];

// Form data
const reportForm = ref<ReportDetails>({
  type: 'bug',
  severity: 'medium',
  title: '',
  description: '',
  stepsToReproduce: '',
  expectedBehavior: '',
  actualBehavior: '',
  additionalNotes: '',
  reportTags: []
});

// Computed properties
const logCount = computed(() => logger.getLogCount());
const userActionCount = computed(() => logger.getUserActions().length);
const recentActions = computed(() => logger.getUserActions().slice(-10));

const viewport = computed(() => ({
  width: window.innerWidth,
  height: window.innerHeight
}));

const isFormValid = computed(() => {
  return reportForm.value.title.trim().length > 0 &&
         reportForm.value.description.trim().length > 0;
});

const currentLanguage = computed(() => {
  return t('locale') === 'fa' ? 'fa' : 'en';
});

// Helper functions
const getTypeIcon = (iconName: string) => {
  const icons: Record<string, any> = {
    'bug': BugIcon,
    'lightbulb': LightbulbIcon,
    'zap': ZapIcon,
    'palette': PaletteIcon,
    'trending-up': TrendingUpIcon,
    'help-circle': HelpCircleIcon,
    'more': HelpCircleIcon
  };
  return icons[iconName] || BugIcon;
};

const getTagType = (tag: string): 'error' | 'default' | 'info' | 'success' | 'warning' | 'primary' => {
  // Create a mapping from translated tags to tag types
  const tagTypeMapping: Record<string, 'error' | 'default' | 'info' | 'success' | 'warning' | 'primary'> = {
    [t('debug.tags.predefined.urgent')]: 'error',
    [t('debug.tags.predefined.fix-needed')]: 'error',
    [t('debug.tags.predefined.error')]: 'error',
    [t('debug.tags.predefined.enhancement')]: 'info',
    [t('debug.tags.predefined.new-feature')]: 'info',
    [t('debug.tags.predefined.idea')]: 'info',
    [t('debug.tags.predefined.slow')]: 'warning',
    [t('debug.tags.predefined.optimization')]: 'warning',
    [t('debug.tags.predefined.speed')]: 'warning',
    [t('debug.tags.predefined.design')]: 'success',
    [t('debug.tags.predefined.user-experience')]: 'success',
    [t('debug.tags.predefined.interface')]: 'success',
    [t('debug.tags.predefined.help')]: 'default',
    [t('debug.tags.predefined.unclear')]: 'default',
    [t('debug.tags.predefined.documentation')]: 'default',
    [t('debug.tags.predefined.better-way')]: 'info',
    [t('debug.tags.predefined.suggestion')]: 'info',
    [t('debug.tags.predefined.miscellaneous')]: 'default',
    [t('debug.tags.predefined.general')]: 'default'
  };
  
  return tagTypeMapping[tag] || 'default';
};

const formatTime = (timestamp: string | null) => {
  if (!timestamp) return '';
  return new Date(timestamp).toLocaleTimeString();
};

// Toggle tag function for clickable predefined tags
const toggleTag = (tag: string) => {
  if (!reportForm.value.reportTags) {
    reportForm.value.reportTags = [];
  }
  
  const tagIndex = reportForm.value.reportTags.indexOf(tag);
  if (tagIndex === -1) {
    // Add tag if not present
    reportForm.value.reportTags.push(tag);
    logger.logUserAction('debug-predefined-tag-added', { tag });
  } else {
    // Remove tag if present
    reportForm.value.reportTags.splice(tagIndex, 1);
    logger.logUserAction('debug-predefined-tag-removed', { tag });
  }
};

// Submit report with offline support
const submitReport = async () => {
  if (!isFormValid.value) {
    message.error(t('debug.formValidationError'));
    return;
  }

  isLoading.value = true;
  logger.logUserAction('debug-report-submit', {
    reportType: reportForm.value.type,
    reportSeverity: reportForm.value.severity,
    hasTags: reportForm.value.reportTags && reportForm.value.reportTags.length > 0,
    isOnline: connectionStore.isConnected
  });

  try {
    // Always try to submit online first, regardless of socket connection status
    const response = await logger.sendLogsToServer(reportForm.value);

    if (response.success) {
      message.success(t('debug.reportSentSuccess', { reportId: response.reportId }));
      showModal.value = false;
      resetForm();
      formDrafts.clearActiveDraft();
    } else if (response.isNetworkError) {
      // Network error detected - store offline
      const reportPayload = {
        logs: logger.getLogs(),
        reportDetails: reportForm.value,
        timestamp: new Date().toISOString(),
        sessionId: `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        diagnosticData: logger.captureDiagnosticData(),
        userIdentification: logger.captureUserIdentification()
      };

      offlineReports.addOfflineReport(reportPayload);
      message.info(t('debug.offlineReportStored'));

      showModal.value = false;
      resetForm();
      formDrafts.clearActiveDraft();
    } else {
      // Server error or validation error - show error message
      message.error(response.message || t('debug.reportSentError'));
    }
  } catch (error) {
    console.error('Debug report submission error:', error);

    // Check if this is a network error by trying to determine the error type
    const isNetworkIssue = !navigator.onLine ||
      (error instanceof Error && (
        error.message.includes('Network Error') ||
        error.message.includes('Failed to fetch') ||
        error.message.includes('ERR_NETWORK')
      ));

    if (isNetworkIssue) {
      // Store offline for network errors
      const reportPayload = {
        logs: logger.getLogs(),
        reportDetails: reportForm.value,
        timestamp: new Date().toISOString(),
        sessionId: `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        diagnosticData: logger.captureDiagnosticData(),
        userIdentification: logger.captureUserIdentification()
      };

      offlineReports.addOfflineReport(reportPayload);
      message.info(t('debug.offlineReportStored'));

      showModal.value = false;
      resetForm();
      formDrafts.clearActiveDraft();
    } else {
      // Other errors - show error message
      message.error(t('debug.reportSentError'));
    }
  } finally {
    isLoading.value = false;
  }
};

const resetForm = () => {
  reportForm.value = {
    type: 'bug',
    severity: 'medium',
    title: '',
    description: '',
    stepsToReproduce: '',
    expectedBehavior: '',
    actualBehavior: '',
    additionalNotes: '',
    reportTags: []
  };
};

// Handle reset form with confirmation
const handleResetForm = () => {
  dialog.warning({
    title: t('debug.resetForm'),
    content: t('debug.resetFormConfirm'),
    positiveText: t('common.confirm'),
    negativeText: t('common.cancel'),
    onPositiveClick: () => {
      resetForm();
      formDrafts.clearActiveDraft();
      message.success(t('debug.resetFormSuccess'));
      logger.logUserAction('debug-form-reset');
    }
  });
};

// Handle form input changes for auto-save
const handleFormInput = () => {
  formDrafts.autoSaveDraft(reportForm.value);
};

// Handle report type change
const handleTypeChange = (newType: string) => {
  reportForm.value.type = newType as any;
  handleFormInput();
  logger.logUserAction('debug-report-type-changed', { newType });
};

// Handle modal opening with draft restoration
const handleModalOpen = () => {
  showModal.value = true;
  currentUrl.value = window.location.href;

  // Check for existing drafts
  if (formDrafts.hasDrafts) {
    dialog.info({
      title: t('debug.restoreDraft'),
      content: t('debug.hasDrafts', { count: formDrafts.draftCount.value }),
      positiveText: t('debug.restoreDraft'),
      negativeText: t('debug.discardDraft'),
      onPositiveClick: () => {
        const latestDraft = formDrafts.getLatestDraft();
        if (latestDraft) {
          reportForm.value = { ...latestDraft };
          message.success(t('debug.draftLoaded'));
          logger.logUserAction('debug-draft-restored');
        }
      },
      onNegativeClick: () => {
        resetForm();
        logger.logUserAction('debug-draft-discarded');
      }
    });
  }
};

// Watch for connection changes
watch(() => connectionStore.isConnected, (isConnected) => {
  if (isConnected && offlineReports.hasOfflineReports) {
    message.info(t('debug.connectionRestored'));
  }
});

// Watch for offline reports processing
watch(() => offlineReports.isProcessingQueue, (isProcessing) => {
  if (isProcessing) {
    message.loading(t('debug.processingOfflineReports'));
  }
});

// Voice recording handlers
const handleTranscriptionComplete = (transcription: string) => {
  logger.logUserAction('voice-transcription-complete', {
    transcriptionLength: transcription.length
  });
  message.success(t('voice.transcriptionComplete'));
};

const handleAnalysisComplete = (report: ReportDetails) => {
  logger.logUserAction('voice-analysis-complete', {
    reportType: report.type,
    confidence: aiAnalysis.confidence.value
  });
  message.success(t('voice.analysisComplete', {
    confidence: Math.round(aiAnalysis.confidence.value * 100)
  }));
};

const handleVoiceError = (error: string) => {
  logger.logUserAction('voice-processing-error', { error });
  message.error(error);
};

const applyAiReport = () => {
  if (aiAnalysis.generatedReport.value) {
    const report = aiAnalysis.generatedReport.value;
    reportForm.value = {
      ...reportForm.value,
      type: report.type,
      severity: report.severity,
      title: report.title,
      description: report.description,
      stepsToReproduce: report.stepsToReproduce || '',
      expectedBehavior: report.expectedBehavior || '',
      actualBehavior: report.actualBehavior || '',
      additionalNotes: report.additionalNotes || '',
      reportTags: [...(reportForm.value.reportTags || []), ...(report.reportTags || [])],
    };

    handleFormInput(); // Trigger auto-save
    logger.logUserAction('ai-report-applied', {
      confidence: aiAnalysis.confidence.value
    });
    message.success(t('voice.reportApplied'));
  }
};

const getTypeLabel = (type?: string) => {
  const typeOption = reportTypes.find(t => t.value === type);
  return typeOption?.label || type;
};

const getSeverityLabel = (severity?: string) => {
  const severityOption = severityOptions.find(s => s.value === severity);
  return severityOption?.label || severity;
};

// Track modal interactions
onMounted(() => {
  logger.logUserAction('debug-report-button-mounted');
});
</script>

<style scoped>
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1 1 0%;
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* Voice Recording Section Styles */
.ai-voice-section {
  border: 2px dashed #e5e7eb;
  border-radius: 0.75rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transition: all 0.3s ease;
}

.ai-voice-section:hover {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.voice-recorder-container {
  display: flex;
  justify-content: center;
  padding: 1rem 0;
}

.ai-results {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
}

.transcription-result {
  animation: fadeInUp 0.5s ease-out;
}

.ai-report-preview {
  animation: fadeInUp 0.5s ease-out 0.2s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark mode adjustments */
.dark .ai-voice-section {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border-color: #475569;
}

.dark .ai-voice-section:hover {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
}

.dark .ai-results {
  border-top-color: #475569;
}
</style>
