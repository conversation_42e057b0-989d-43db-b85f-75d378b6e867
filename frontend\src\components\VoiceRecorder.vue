<template>
  <div class="voice-recorder">
    <!-- Recording Controls -->
    <div class="recording-controls">
      <!-- Record Button -->
      <n-button
        v-if="!isRecording && !hasRecording"
        type="primary"
        size="large"
        circle
        :disabled="!isSupported || isProcessing"
        @click="handleStartRecording"
        class="record-button"
      >
        <template #icon>
          <n-icon size="24">
            <MicrophoneIcon />
          </n-icon>
        </template>
      </n-button>

      <!-- Stop Button -->
      <n-button
        v-if="isRecording"
        type="error"
        size="large"
        circle
        @click="handleStopRecording"
        class="stop-button"
      >
        <template #icon>
          <n-icon size="24">
            <StopIcon />
          </n-icon>
        </template>
      </n-button>

      <!-- Pause/Resume Button -->
      <n-button
        v-if="isRecording"
        type="warning"
        size="medium"
        circle
        @click="handlePauseResume"
        class="pause-resume-button"
      >
        <template #icon>
          <n-icon size="20">
            <PauseIcon v-if="!isPaused" />
            <PlayIcon v-else />
          </n-icon>
        </template>
      </n-button>
    </div>

    <!-- Recording Status -->
    <div v-if="isRecording || hasRecording" class="recording-status">
      <!-- Duration Display -->
      <div class="duration-display">
        <n-icon class="duration-icon" :class="{ 'recording-pulse': isRecording && !isPaused }">
          <MicrophoneIcon />
        </n-icon>
        <span class="duration-text">{{ formattedDuration }}</span>
        <span v-if="isPaused" class="paused-indicator">{{ $t('voice.paused') }}</span>
      </div>

      <!-- Progress Bar -->
      <n-progress
        type="line"
        :percentage="(duration / maxDuration) * 100"
        :color="getProgressColor()"
        :show-indicator="false"
        class="duration-progress"
      />

      <!-- Max Duration Warning -->
      <div v-if="duration > maxDuration * 0.8" class="duration-warning">
        <n-icon size="16" color="#f0a020">
          <WarningIcon />
        </n-icon>
        <span>{{ $t('voice.nearMaxDuration', { remaining: Math.ceil(maxDuration - duration) }) }}</span>
      </div>
    </div>

    <!-- Playback Controls -->
    <div v-if="hasRecording && !isRecording" class="playback-controls">
      <n-button
        size="small"
        @click="playRecording"
        :disabled="!audioUrl"
      >
        <template #icon>
          <n-icon>
            <PlayIcon />
          </n-icon>
        </template>
        {{ $t('voice.playback') }}
      </n-button>

      <n-button
        size="small"
        type="error"
        ghost
        @click="handleCancelRecording"
      >
        <template #icon>
          <n-icon>
            <DeleteIcon />
          </n-icon>
        </template>
        {{ $t('voice.delete') }}
      </n-button>
    </div>

    <!-- Processing Status -->
    <div v-if="isProcessing" class="processing-status">
      <n-spin size="small" />
      <span class="processing-text">
        <span v-if="isTranscribing">{{ $t('voice.transcribing') }}</span>
        <span v-else-if="isAnalyzing">{{ $t('voice.analyzing') }}</span>
        <span v-else>{{ $t('voice.processing') }}</span>
      </span>
    </div>

    <!-- Error Display -->
    <div v-if="error" class="error-display">
      <n-alert type="error" :show-icon="false" closable @close="clearError">
        {{ error }}
      </n-alert>
    </div>

    <!-- Not Supported Warning -->
    <div v-if="!isSupported" class="not-supported-warning">
      <n-alert type="warning" :show-icon="true">
        {{ $t('voice.notSupported') }}
      </n-alert>
    </div>

    <!-- Hidden Audio Element for Playback -->
    <audio
      ref="audioElement"
      :src="audioUrl"
      @ended="onPlaybackEnded"
      style="display: none;"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useMessage } from 'naive-ui';
import { useVoiceRecording } from '@/composables/useVoiceRecording';
import { useAiAnalysis } from '@/composables/useAiAnalysis';
import {
  Microphone as MicrophoneIcon,
  PlayerStop as StopIcon,
  PlayerPause as PauseIcon,
  PlayerPlay as PlayIcon,
  AlertTriangle as WarningIcon,
  Trash as DeleteIcon,
} from '@vicons/tabler';

// Props
interface Props {
  language?: string;
  autoProcess?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  language: 'en',
  autoProcess: true,
});

// Emits
interface Emits {
  (e: 'recording-started'): void;
  (e: 'recording-stopped', audioData: { base64: string; mimeType: string; duration: number }): void;
  (e: 'recording-cancelled'): void;
  (e: 'transcription-complete', transcription: string): void;
  (e: 'analysis-complete', report: any): void;
  (e: 'processing-error', error: string): void;
}

const emit = defineEmits<Emits>();

const { t } = useI18n();
const message = useMessage();

// Composables
const {
  isRecording,
  isPaused,
  isSupported,
  duration,
  formattedDuration,
  audioUrl,
  hasRecording,
  canRecord,
  canStop,
  canPause,
  canResume,
  startRecording,
  stopRecording,
  pauseRecording,
  resumeRecording,
  cancelRecording,
  getAudioAsBase64,
  getAudioMetadata,
  maxDuration,
} = useVoiceRecording();

const {
  isProcessing,
  isTranscribing,
  isAnalyzing,
  transcription,
  generatedReport,
  error: aiError,
  processVoiceToReport,
  clearState: clearAiState,
} = useAiAnalysis();

// Local state
const audioElement = ref<HTMLAudioElement | null>(null);
const error = ref<string>('');

// Methods
const handleStartRecording = async () => {
  const success = await startRecording();
  if (success) {
    emit('recording-started');
    clearError();
    clearAiState();
  }
};

const handleStopRecording = async () => {
  stopRecording();
  
  // Wait a bit for the recording to be processed
  setTimeout(async () => {
    const audioData = await getAudioAsBase64();
    const metadata = getAudioMetadata();
    
    if (audioData && metadata) {
      emit('recording-stopped', {
        base64: audioData,
        mimeType: metadata.mimeType,
        duration: metadata.duration,
      });

      // Auto-process if enabled
      if (props.autoProcess) {
        await processRecording(audioData, metadata.mimeType, metadata.duration);
      }
    }
  }, 100);
};

const handlePauseResume = () => {
  if (isPaused.value) {
    resumeRecording();
  } else {
    pauseRecording();
  }
};

const handleCancelRecording = () => {
  cancelRecording();
  clearError();
  clearAiState();
  emit('recording-cancelled');
};

const processRecording = async (audioBase64: string, mimeType: string, duration: number) => {
  try {
    const success = await processVoiceToReport(audioBase64, mimeType, duration, props.language);
    
    if (success && transcription.value) {
      emit('transcription-complete', transcription.value);
      
      if (generatedReport.value) {
        emit('analysis-complete', generatedReport.value);
      }
    }
  } catch (err: any) {
    error.value = err.message || t('voice.processingError');
    emit('processing-error', error.value);
  }
};

const playRecording = () => {
  if (audioElement.value && audioUrl.value) {
    audioElement.value.play().catch((err) => {
      console.error('Failed to play audio:', err);
      message.error(t('voice.playbackError'));
    });
  }
};

const onPlaybackEnded = () => {
  // Playback ended - could emit an event if needed
};

const clearError = () => {
  error.value = '';
};

const getProgressColor = () => {
  const percentage = (duration.value / maxDuration) * 100;
  if (percentage > 90) return '#ef4444'; // Red
  if (percentage > 80) return '#f59e0b'; // Orange
  return '#10b981'; // Green
};

// Watch for AI errors
watch(aiError, (newError) => {
  if (newError) {
    error.value = newError;
  }
});

// Expose methods for parent component
defineExpose({
  startRecording: handleStartRecording,
  stopRecording: handleStopRecording,
  cancelRecording: handleCancelRecording,
  processRecording,
  clearError,
});
</script>

<style scoped>
.voice-recorder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
}

.recording-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.record-button {
  width: 64px;
  height: 64px;
}

.stop-button {
  width: 64px;
  height: 64px;
}

.pause-resume-button {
  width: 48px;
  height: 48px;
}

.recording-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  max-width: 300px;
}

.duration-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 500;
}

.duration-icon {
  color: #ef4444;
}

.recording-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.paused-indicator {
  font-size: 0.9rem;
  color: #f59e0b;
  font-style: italic;
}

.duration-progress {
  width: 100%;
}

.duration-warning {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: #f59e0b;
}

.playback-controls {
  display: flex;
  gap: 0.5rem;
}

.processing-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.9rem;
}

.error-display {
  width: 100%;
  max-width: 400px;
}

.not-supported-warning {
  width: 100%;
  max-width: 400px;
}
</style>
