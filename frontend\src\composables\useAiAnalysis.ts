import { ref, computed } from 'vue';
import { useMessage } from 'naive-ui';
import { useI18n } from 'vue-i18n';
import { aiApiService } from '@/services/aiApiService';
import type { ReportDetails } from '@/types/logging';

/**
 * AI Analysis Composable
 * Handles voice-to-text transcription and AI-powered bug report generation
 */
export function useAiAnalysis() {
  const { t } = useI18n();
  const message = useMessage();

  // State
  const isProcessing = ref(false);
  const isTranscribing = ref(false);
  const isAnalyzing = ref(false);
  const transcription = ref<string>('');
  const generatedReport = ref<ReportDetails | null>(null);
  const confidence = ref<number>(0);
  const processingTime = ref<number>(0);
  const error = ref<string>('');

  // AI service availability
  const isAvailable = ref<boolean>(false);
  const supportedLanguages = ref<string[]>(['en', 'fa']);
  const maxAudioDuration = ref<number>(60);

  /**
   * Check AI service availability
   */
  const checkAvailability = async (): Promise<boolean> => {
    try {
      const status = await aiApiService.getStatus();
      if (status.success) {
        isAvailable.value = status.status.features.voiceToReport;
        supportedLanguages.value = status.status.supportedLanguages || ['en', 'fa'];
        maxAudioDuration.value = status.status.maxAudioDuration || 60;
        return isAvailable.value;
      }
      return false;
    } catch (error) {
      console.error('Failed to check AI service availability:', error);
      isAvailable.value = false;
      return false;
    }
  };

  /**
   * Process voice recording to generate bug report
   */
  const processVoiceToReport = async (
    audioBase64: string,
    mimeType: string,
    duration: number,
    language: string = 'en'
  ): Promise<boolean> => {
    if (!isAvailable.value) {
      error.value = t('ai.serviceNotAvailable');
      message.error(error.value);
      return false;
    }

    isProcessing.value = true;
    isTranscribing.value = true;
    isAnalyzing.value = false;
    error.value = '';
    transcription.value = '';
    generatedReport.value = null;
    confidence.value = 0;

    try {
      // Get user context
      const userContext = {
        currentPage: window.location.href,
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
      };

      // Call the combined voice-to-report API
      const result = await aiApiService.processVoiceToReport({
        audioData: audioBase64,
        mimeType,
        duration,
        language,
        userContext,
      });

      isTranscribing.value = false;
      isAnalyzing.value = true;

      if (result.success && result.generatedReport) {
        transcription.value = result.transcription || '';
        generatedReport.value = {
          type: result.generatedReport.suggestedType,
          severity: result.generatedReport.suggestedSeverity,
          title: result.generatedReport.title,
          description: result.generatedReport.description,
          stepsToReproduce: result.generatedReport.stepsToReproduce || '',
          expectedBehavior: result.generatedReport.expectedBehavior || '',
          actualBehavior: result.generatedReport.actualBehavior || '',
          additionalNotes: result.generatedReport.additionalNotes || '',
          reportTags: result.generatedReport.suggestedTags || [],
        };
        confidence.value = result.generatedReport.confidence;
        processingTime.value = result.processingTime || 0;

        message.success(t('ai.analysisComplete', { 
          confidence: Math.round(confidence.value * 100) 
        }));
        return true;
      } else {
        error.value = result.error || t('ai.analysisError');
        message.error(error.value);
        return false;
      }
    } catch (err: any) {
      console.error('Voice-to-report processing failed:', err);
      error.value = err.message || t('ai.processingError');
      message.error(error.value);
      return false;
    } finally {
      isProcessing.value = false;
      isTranscribing.value = false;
      isAnalyzing.value = false;
    }
  };

  /**
   * Transcribe audio only (without AI analysis)
   */
  const transcribeAudio = async (
    audioBase64: string,
    mimeType: string,
    duration: number,
    language: string = 'en'
  ): Promise<string | null> => {
    if (!isAvailable.value) {
      error.value = t('ai.serviceNotAvailable');
      message.error(error.value);
      return null;
    }

    isTranscribing.value = true;
    error.value = '';

    try {
      const result = await aiApiService.transcribeAudio({
        audioData: audioBase64,
        mimeType,
        duration,
        language,
      });

      if (result.success && result.transcription) {
        transcription.value = result.transcription;
        return result.transcription;
      } else {
        error.value = result.error || t('ai.transcriptionError');
        message.error(error.value);
        return null;
      }
    } catch (err: any) {
      console.error('Audio transcription failed:', err);
      error.value = err.message || t('ai.transcriptionError');
      message.error(error.value);
      return null;
    } finally {
      isTranscribing.value = false;
    }
  };

  /**
   * Analyze transcription text to generate bug report
   */
  const analyzeTranscription = async (
    text: string,
    language: string = 'en'
  ): Promise<ReportDetails | null> => {
    if (!isAvailable.value) {
      error.value = t('ai.serviceNotAvailable');
      message.error(error.value);
      return null;
    }

    isAnalyzing.value = true;
    error.value = '';

    try {
      const userContext = {
        currentPage: window.location.href,
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
      };

      const result = await aiApiService.analyzeTranscription({
        transcription: text,
        language,
        userContext,
      });

      if (result.success && result.generatedReport) {
        generatedReport.value = {
          type: result.generatedReport.suggestedType,
          severity: result.generatedReport.suggestedSeverity,
          title: result.generatedReport.title,
          description: result.generatedReport.description,
          stepsToReproduce: result.generatedReport.stepsToReproduce || '',
          expectedBehavior: result.generatedReport.expectedBehavior || '',
          actualBehavior: result.generatedReport.actualBehavior || '',
          additionalNotes: result.generatedReport.additionalNotes || '',
          reportTags: result.generatedReport.suggestedTags || [],
        };
        confidence.value = result.generatedReport.confidence;
        processingTime.value = result.processingTime || 0;

        return generatedReport.value;
      } else {
        error.value = result.error || t('ai.analysisError');
        message.error(error.value);
        return null;
      }
    } catch (err: any) {
      console.error('Transcription analysis failed:', err);
      error.value = err.message || t('ai.analysisError');
      message.error(error.value);
      return null;
    } finally {
      isAnalyzing.value = false;
    }
  };

  /**
   * Clear all state
   */
  const clearState = () => {
    transcription.value = '';
    generatedReport.value = null;
    confidence.value = 0;
    processingTime.value = 0;
    error.value = '';
    isProcessing.value = false;
    isTranscribing.value = false;
    isAnalyzing.value = false;
  };

  // Computed properties
  const hasTranscription = computed(() => !!transcription.value);
  const hasGeneratedReport = computed(() => !!generatedReport.value);
  const confidencePercentage = computed(() => Math.round(confidence.value * 100));
  const processingTimeFormatted = computed(() => {
    if (processingTime.value < 1000) {
      return `${processingTime.value}ms`;
    }
    return `${(processingTime.value / 1000).toFixed(1)}s`;
  });

  // Initialize by checking availability
  checkAvailability();

  return {
    // State
    isProcessing: computed(() => isProcessing.value),
    isTranscribing: computed(() => isTranscribing.value),
    isAnalyzing: computed(() => isAnalyzing.value),
    transcription: computed(() => transcription.value),
    generatedReport: computed(() => generatedReport.value),
    confidence: computed(() => confidence.value),
    confidencePercentage,
    processingTime: computed(() => processingTime.value),
    processingTimeFormatted,
    error: computed(() => error.value),

    // Availability
    isAvailable: computed(() => isAvailable.value),
    supportedLanguages: computed(() => supportedLanguages.value),
    maxAudioDuration: computed(() => maxAudioDuration.value),

    // Computed flags
    hasTranscription,
    hasGeneratedReport,

    // Methods
    checkAvailability,
    processVoiceToReport,
    transcribeAudio,
    analyzeTranscription,
    clearState,
  };
}
