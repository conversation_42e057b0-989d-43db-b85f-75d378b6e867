import { ref, computed, onUnmounted } from 'vue';
import { useMessage } from 'naive-ui';
import { useI18n } from 'vue-i18n';

/**
 * Voice Recording Composable
 * Handles audio recording using browser MediaRecorder API with VueUse integration
 */
export function useVoiceRecording() {
  const { t } = useI18n();
  const message = useMessage();

  // Recording state
  const isRecording = ref(false);
  const isPaused = ref(false);
  const isSupported = ref(false);
  const duration = ref(0);
  const audioBlob = ref<Blob | null>(null);
  const audioUrl = ref<string | null>(null);

  // Recording configuration
  const maxDuration = 60; // 60 seconds max
  const sampleRate = 16000; // Standard for speech recognition

  // Internal state
  let mediaRecorder: MediaRecorder | null = null;
  let mediaStream: MediaStream | null = null;
  let durationInterval: NodeJS.Timeout | null = null;
  let audioChunks: Blob[] = [];

  // Check browser support
  const checkSupport = () => {
    isSupported.value = !!(
      navigator.mediaDevices &&
      navigator.mediaDevices.getUserMedia &&
      window.MediaRecorder
    );
    return isSupported.value;
  };

  // Get supported MIME types for audio recording
  const getSupportedMimeType = (): string => {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/ogg;codecs=opus',
      'audio/ogg',
      'audio/wav',
      'audio/mp4',
      'audio/mpeg',
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type;
      }
    }

    return 'audio/webm'; // Fallback
  };

  // Start recording
  const startRecording = async (): Promise<boolean> => {
    try {
      if (!checkSupport()) {
        message.error(t('voice.notSupported'));
        return false;
      }

      // Request microphone access
      mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: sampleRate,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      });

      // Get supported MIME type
      const mimeType = getSupportedMimeType();

      // Create MediaRecorder
      mediaRecorder = new MediaRecorder(mediaStream, {
        mimeType,
        audioBitsPerSecond: 128000,
      });

      // Reset state
      audioChunks = [];
      duration.value = 0;
      audioBlob.value = null;
      audioUrl.value = null;

      // Set up event handlers
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunks.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        // Create blob from chunks
        const mimeType = mediaRecorder?.mimeType || getSupportedMimeType();
        audioBlob.value = new Blob(audioChunks, { type: mimeType });
        
        // Create URL for playback
        if (audioUrl.value) {
          URL.revokeObjectURL(audioUrl.value);
        }
        audioUrl.value = URL.createObjectURL(audioBlob.value);

        // Clean up
        stopDurationTimer();
        cleanupMediaStream();
      };

      mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event);
        message.error(t('voice.recordingError'));
        stopRecording();
      };

      // Start recording
      mediaRecorder.start(100); // Collect data every 100ms
      isRecording.value = true;
      isPaused.value = false;

      // Start duration timer
      startDurationTimer();

      return true;
    } catch (error: any) {
      console.error('Failed to start recording:', error);
      
      if (error.name === 'NotAllowedError') {
        message.error(t('voice.permissionDenied'));
      } else if (error.name === 'NotFoundError') {
        message.error(t('voice.noMicrophone'));
      } else {
        message.error(t('voice.recordingError'));
      }
      
      cleanupMediaStream();
      return false;
    }
  };

  // Stop recording
  const stopRecording = () => {
    if (mediaRecorder && isRecording.value) {
      mediaRecorder.stop();
      isRecording.value = false;
      isPaused.value = false;
    }
  };

  // Pause recording
  const pauseRecording = () => {
    if (mediaRecorder && isRecording.value && !isPaused.value) {
      mediaRecorder.pause();
      isPaused.value = true;
      stopDurationTimer();
    }
  };

  // Resume recording
  const resumeRecording = () => {
    if (mediaRecorder && isRecording.value && isPaused.value) {
      mediaRecorder.resume();
      isPaused.value = false;
      startDurationTimer();
    }
  };

  // Cancel recording
  const cancelRecording = () => {
    if (mediaRecorder) {
      mediaRecorder.stop();
      isRecording.value = false;
      isPaused.value = false;
      audioBlob.value = null;
      audioUrl.value = null;
      audioChunks = [];
      duration.value = 0;
    }
  };

  // Start duration timer
  const startDurationTimer = () => {
    durationInterval = setInterval(() => {
      duration.value += 0.1;
      
      // Auto-stop at max duration
      if (duration.value >= maxDuration) {
        stopRecording();
        message.warning(t('voice.maxDurationReached', { duration: maxDuration }));
      }
    }, 100);
  };

  // Stop duration timer
  const stopDurationTimer = () => {
    if (durationInterval) {
      clearInterval(durationInterval);
      durationInterval = null;
    }
  };

  // Clean up media stream
  const cleanupMediaStream = () => {
    if (mediaStream) {
      mediaStream.getTracks().forEach(track => track.stop());
      mediaStream = null;
    }
  };

  // Convert audio blob to base64
  const getAudioAsBase64 = (): Promise<string | null> => {
    return new Promise((resolve) => {
      if (!audioBlob.value) {
        resolve(null);
        return;
      }

      const reader = new FileReader();
      reader.onloadend = () => {
        const base64 = reader.result as string;
        // Remove data URL prefix (e.g., "data:audio/webm;base64,")
        const base64Data = base64.split(',')[1];
        resolve(base64Data);
      };
      reader.onerror = () => resolve(null);
      reader.readAsDataURL(audioBlob.value);
    });
  };

  // Get audio metadata
  const getAudioMetadata = () => {
    if (!audioBlob.value || !mediaRecorder) {
      return null;
    }

    return {
      mimeType: mediaRecorder.mimeType || getSupportedMimeType(),
      size: audioBlob.value.size,
      duration: duration.value,
    };
  };

  // Computed properties
  const formattedDuration = computed(() => {
    const minutes = Math.floor(duration.value / 60);
    const seconds = Math.floor(duration.value % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  });

  const canRecord = computed(() => isSupported.value && !isRecording.value);
  const canStop = computed(() => isRecording.value);
  const canPause = computed(() => isRecording.value && !isPaused.value);
  const canResume = computed(() => isRecording.value && isPaused.value);
  const hasRecording = computed(() => !!audioBlob.value);

  // Cleanup on unmount
  onUnmounted(() => {
    stopRecording();
    stopDurationTimer();
    cleanupMediaStream();
    
    if (audioUrl.value) {
      URL.revokeObjectURL(audioUrl.value);
    }
  });

  // Initialize
  checkSupport();

  return {
    // State
    isRecording: computed(() => isRecording.value),
    isPaused: computed(() => isPaused.value),
    isSupported: computed(() => isSupported.value),
    duration: computed(() => duration.value),
    formattedDuration,
    audioUrl: computed(() => audioUrl.value),
    hasRecording,

    // Computed flags
    canRecord,
    canStop,
    canPause,
    canResume,

    // Methods
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    cancelRecording,
    getAudioAsBase64,
    getAudioMetadata,

    // Configuration
    maxDuration,
    getSupportedMimeType,
  };
}
