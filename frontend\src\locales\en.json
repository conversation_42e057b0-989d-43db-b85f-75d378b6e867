{"app": {"title": "Arz<PERSON>ni", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "cancel": "Cancel", "confirm": "Confirm", "close": "Close", "save": "Save", "edit": "Edit", "delete": "Delete", "add": "Add", "search": "Search", "filter": "Filter", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "buy": "Buy", "sell": "<PERSON>ll"}, "auth": {"welcomeBack": "Welcome back to ArzAni", "signIn": "Sign In", "signUp": "Sign Up", "logout": "Logout", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "firstName": "First Name", "lastName": "Last Name", "username": "Username", "phoneNumber": "Phone Number", "enterEmail": "Enter your email", "enterPassword": "Enter your password", "enterPasswordMinChars": "Enter your password (min 8 characters)", "enterConfirmPassword": "Confirm your password", "enterFirstName": "Enter your first name", "enterLastName": "Enter your last name", "enterUsername": "Enter your username", "usernameAvailable": "Username is available", "usernameTaken": "Username is already taken", "usernameCheckError": "Unable to check username availability", "you": "You", "otherParty": "Other Party", "otherUser": "Other User", "notAvailable": "N/A", "usernameSuggestions": "Suggestions", "emailAvailable": "Email is available", "emailTaken": "Email is already registered", "emailCheckError": "Unable to check email availability", "enterPhoneNumber": "Enter your phone number", "noAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "registerHere": "Register here", "loginHere": "Login here", "joinCommunity": "Join our trusted community", "agreeToTerms": "I agree to the", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy", "agree": "I agree", "forgotPassword": "Forgot your password?", "resetPassword": "Reset Password", "rememberMe": "Remember me", "loginError": "<PERSON><PERSON>", "registrationError": "Registration Error", "emailVerificationSent": "Email verification sent", "phoneVerificationRequired": "Phone verification required", "accountCreated": "Account created", "loginSuccessful": "Login successful", "createAccount": "Create Account", "emailNotVerified": "Your email is not verified. Please check your verification email.", "resendVerification": "Resend Verification Email", "verificationEmailSent": "Verification email sent! Please check your inbox."}, "navigation": {"home": "Home", "browse": "Browse Offers", "myOffers": "My Offers", "createOffer": "Create Offer", "profile": "Profile", "notifications": "Notifications", "chat": "Cha<PERSON>", "help": "Help", "settings": "Settings", "about": "About Us", "contact": "Contact Us"}, "landing": {"heroTitle": "Arz<PERSON>ni", "heroSubtitle": "Secure currency exchange between different countries and Iran", "heroDescription": "Secure peer-to-peer currency exchange platform for Iranian diaspora worldwide. Connect with trusted partners for international currency exchange through our reputation-based system.", "getStartedFree": "Get Started Free", "whyChoose": "Why <PERSON><PERSON>?", "secureTitle": "Secure & Trusted", "secureDesc": "Built-in reputation system and secure chat ensure safe transactions with verified users.", "reputationTitle": "Reputation-Based Pricing", "reputationDesc": "Get better rates based on your trust level and transaction history in the global community.", "noFeesTitle": "No Hidden Fees", "noFeesDesc": "Direct peer-to-peer matching with transparent rates. No intermediary fees or markups.", "verificationTitle": "Easy Verification", "verificationDesc": "Simple email and phone verification process to join the trusted community.", "howItWorks": "How It Works", "step1Title": "Sign Up & Verify", "step1Desc": "Create your account and verify your email and phone number to join the trusted community.", "step2Title": "Create or Browse Offers", "step2Desc": "Post your own exchange offer or browse available offers from other verified users.", "step3Title": "Connect & Chat", "step3Desc": "Show interest in offers and connect via secure in-app chat to coordinate your exchange.", "step4Title": "Complete Exchange", "step4Desc": "Complete your exchange through your respective banks and rate your experience.", "readyToStart": "Ready to Start Exchanging?", "ctaDescription": "Join thousands of Iranian diaspora members worldwide who trust ArzAni for their currency exchange needs.", "createAccount": "Create Your Account", "footerDescription": "Secure peer-to-peer currency exchange platform", "footerCopyright": "© 2025 ArzAni. All rights reserved.", "features": "Features", "testimonials": "Testimonials", "joinNow": "Join Now", "heroExchange": "Currency Exchange Made", "heroSmarter": "Smarter & Safer", "startExchanging": "Start Exchanging", "learnMore": "Learn More", "lowerFees": "Lower Fees", "activeUsers": "Active Users", "successRate": "Success Rate", "quickExchange": "Quick Exchange", "liveRates": "Live Rates", "internationalCurrency": "International Currency", "iranianRial": "Iranian Rial", "findExchangePartner": "Find Exchange Partner", "featuresSubtitle": "Discover what makes <PERSON><PERSON><PERSON><PERSON> the trusted choice for currency exchange", "betterRatesTitle": "Better Exchange Rates", "betterRatesDesc": "Get competitive rates based on your reputation and trust level in our global community.", "directConnectionTitle": "Direct P2P Connection", "directConnectionDesc": "Connect directly with exchange partners without intermediaries or hidden fees.", "globalNetworkTitle": "Global Network", "globalNetworkDesc": "Access a worldwide network of trusted Iranian diaspora members for currency exchange.", "smartMatchingTitle": "Smart Matching", "smartMatchingDesc": "Advanced algorithms match you with the best exchange partners based on your preferences.", "easyVerificationTitle": "Easy Verification", "easyVerificationDesc": "Simple verification process ensures security while maintaining user-friendly experience.", "howItWorksSubtitle": "Simple steps to start exchanging currency safely", "testimonialName1": "<PERSON>", "testimonialLocation1": "Toronto, Canada", "testimonialQuote1": "<PERSON><PERSON><PERSON><PERSON> made sending money to my family in Iran so much easier and more affordable. The reputation system gives me confidence in every transaction.", "testimonialName2": "<PERSON>", "testimonialLocation2": "London, UK", "testimonialQuote2": "Finally, a platform that understands the Iranian diaspora's needs. ArzAni provides excellent rates and secure transactions every time."}, "offers": {"title": "Offers", "myOffers": "My Offers", "createOffer": "Create <PERSON>er", "browseOffers": "Browse Offers", "editOffer": "Edit Offer", "deleteOffer": "Delete Offer", "offerType": "Offer Type", "amount": "Amount", "rate": "Rate", "currency": "<PERSON><PERSON><PERSON><PERSON>", "fromCurrency": "From Currency", "toCurrency": "To <PERSON><PERSON><PERSON><PERSON>", "description": "Description", "location": "Location", "createdAt": "Created At", "updatedAt": "Updated At", "status": "Status", "overallStatus": "Overall Status", "active": "Active", "inactive": "Inactive", "completed": "Completed", "cancelled": "Cancelled", "showInterest": "Show Interest", "confirmShowInterest": "Are you sure you want to show interest in this offer?", "interestShown": "Interest Shown", "acceptInterest": "Accept Interest", "declineInterest": "Decline Interest", "chatWithUser": "Chat with User", "buyType": "Buy", "sellType": "<PERSON>ll", "irr": "Iranian Rial", "usd": "US Dollar", "eur": "Euro", "gbp": "British Pound", "cad": "Canadian Dollar", "aud": "Australian Dollar", "aed": "UAE Dirham", "exchangeRate": "Exchange Rate", "totalAmount": "Total Amount", "reputation": "Reputation", "verified": "Verified", "unverified": "Unverified", "offerDetails": "Offer Details", "edit": "Edit", "activate": "Activate", "deactivate": "Deactivate", "activatedSuccessfully": "Offer activated successfully", "deactivatedSuccessfully": "Offer deactivated successfully", "interestExpressedSuccessfully": "Interest expressed successfully", "inactiveOfferMessage": "This offer is currently inactive", "receivedInterests": "Received Interests", "createdBy": "Created By", "noOffersFound": "No offers found", "loadingOffers": "Loading offers...", "buying": "Buying", "selling": "Selling", "creator": "Creator", "unknownUser": "Unknown User", "level": "Lvl", "baseRate": "Base Rate (matched rep)", "yourApplicableRate": "Your Applicable Rate", "rateAdvantage": "Rate Advantage!", "rateAdjustment": "Rate Adjustment", "bonusText": "You get a bonus of {amount} IRR/CAD! Your reputation level provides a better rate.", "penaltyText": "A {amount} IRR/CAD rate adjustment applies. Improve your reputation for potentially better rates.", "transactionCompleted": "Transaction Completed", "transactionCompletedMessage": "This transaction has been completed successfully.", "continueTransaction": "Continue Transaction", "transactionNegotiating": "Transaction in negotiation phase.", "transactionInProgress": "Transaction in progress.", "chatNow": "Chat Now", "interestAcceptedMessage": "Your interest has been accepted! Start chatting to coordinate your exchange.", "interestPendingMessage": "Your interest is pending approval from the offer creator.", "interestDeclinedMessage": "Your interest was declined. You can still show interest in other offers.", "showingInterest": "Showing Interest...", "cannotShowInterest": "Cannot show interest in your own offer.", "alreadyShowedInterest": "You have already shown interest in this offer.", "chatNotAvailable": "Chat session not available", "openingChatSession": "Opening chat session: {sessionId}", "transactionStatusInProgress": "In Progress", "transactionStatusNegotiating": "Negotiating", "transactionStatusCompleted": "Completed", "transactionStatusCancelled": "Cancelled", "interestRequests": "Interest Requests", "offersWithInterests": "Offers with Interests", "interestsReceived": "Interests Received", "interestedUser": "Interested User", "interestStatus": "Status", "declineReason": "Reason", "chatId": "Chat ID", "goToChat": "Go to Chat", "negotiatingStatus": "Negotiating", "inProgressStatus": "In Progress", "offer": "Offer", "atRate": "at rate", "created": "Created", "refreshOffers": "Refresh offers", "noInterestRequestsCurrently": "No interest requests currently.", "noOffersCreated": "You have not created any offers yet.", "yourRate": "Your Rate", "by": "By", "bonus": "Bonus", "penalty": "Penalty", "adjustment": "Adjustment", "type": "Type", "amountCAD": "Amount (CAD)", "actions": "Actions", "statusActive": "Active", "statusInactive": "Inactive", "statusUpdated": "Status updated"}, "offerDetails": {"title": "Offer Details", "loading": "Loading...", "loadingOfferDetails": "Loading offer details...", "errorLoadingOffer": "Error Loading Offer", "offerUnavailable": "Offer Unavailable", "offerCancelled": "Offer Cancelled", "offerCompleted": "Offer Completed", "offerDeactivated": "Offer Deactivated", "offerNoLongerAvailable": "Offer No Longer Available", "offerCancelledMessage": "This offer has been cancelled by the creator.", "offerCompletedMessage": "This offer has been completed with another user.", "offerDeactivatedMessage": "This offer has been temporarily deactivated by the creator.", "offerNotAvailableMessage": "This offer is no longer available for interaction.", "type": "Type", "amount": "Amount", "baseRate": "Base Rate", "yourRate": "Your Rate", "createdBy": "Created By", "reputationLevel": "Reputation Level", "level": "Level", "baseRateApplies": "Base Rate Applies", "baseRateMessage": "You are getting the standard base rate for this offer.", "rateAdvantage": "Rate Advantage!", "rateAdjustment": "Rate Adjustment", "rateAdvantageMessage": "You get a bonus of {amount} IRR/CAD! Your reputation level provides a better rate.", "rateAdjustmentMessage": "A {amount} IRR/CAD rate adjustment applies. This may be due to your reputation level or the offer's terms.", "phoneVerificationRequired": "Phone Verification Required", "phoneVerificationMessage": "You need to verify your phone number before you can show interest in offers.", "goToProfileToVerify": "Go to Profile to Verify Phone", "showInterest": "Show Interest", "interestAlreadyShown": "Interest Already Shown", "showingInterest": "Showing Interest...", "verifyPhoneToShowInterest": "Verify Phone to Show Interest", "pleaseVerifyPhoneFirst": "Please verify your phone number first. Go to Profile → Phone Verification.", "alreadyShownInterest": "You have already shown interest in this offer.", "phoneVerificationRequiredToShow": "Phone verification is required to show interest in offers. Please verify your phone number in your profile.", "failedToShowInterest": "Failed to show interest in offer.", "offerNoLongerActive": "This offer has been {status}.", "offerDetailsUpdated": "Offer details have been updated. Refreshing...", "interestShownSuccessfully": "Interest shown successfully!"}, "interests": {"acceptedSuccessfully": "Interest accepted successfully", "declinedSuccessfully": "Interest declined successfully", "alreadyExpressed": "Interest Already Expressed", "status": "Status", "statuses": {"pending": "Pending", "accepted": "Accepted", "declined": "Declined"}}, "status": {"pending": "Pending", "declined": "Declined", "cancelled": "Cancelled", "accepted": "Accepted", "complete": "Complete", "negotiating": "Negotiating", "inProgress": "In Progress", "settingUp": "Setting Up", "termsAgreed": "Terms Agreed", "disputed": "Disputed", "active": "Active"}, "offerForm": {"understandingTieredPricing": "Understanding Tiered Pricing", "baseRateExplanation": "Your offer sets a <b>base rate</b> for users with similar reputation ({tier}).", "adjustmentExplanation": "You can adjust this rate for other reputation tiers by entering a <b>positive percentage</b> (e.g., 1.25 for 1.25%). The system automatically applies this adjustment.", "lowerTierWorseRates": "Less trusted users ALWAYS get <span class=\"text-worse\">worse rates</span>.", "higherTierBetterRates": "More trusted users ALWAYS get <span class=\"text-better\">better rates</span>.", "offerBasics": "Offer Basics", "offerType": "Offer Type", "sellCAD": "Sell CAD", "buyCAD": "Buy CAD", "currencyPair": "<PERSON><PERSON><PERSON><PERSON>", "amountCAD": "Amount (CAD)", "baseExchangeRate": "Base Exchange Rate (IRR/CAD)", "baseRatePlaceholder": "Base rate for {tier} users, e.g., 150000", "baseRateFeedback": "This is the standard rate for users with similar reputation ({tier}). Adjustments are relative to this base rate.", "tieredAdjustments": "Tiered Adjustments", "forLowerReputationUsers": "For Lower Reputation Users ({tier})", "forHigherReputationUsers": "For Higher Reputation Users ({tier})", "definePenaltyPercentage": "Define Penalty Percentage:", "defineBonusPercentage": "Define Bonus Percentage:", "percentagePlaceholder": "e.g., 1.25", "calculatedImpact": "Calculated Impact", "yourBaseRate": "Your Base Rate ({tier}): <span class=\"rate-value\">{rate} IRR/CAD</span>", "adjustedRate": "{tier} Adjusted Rate: <span class=\"rate-value\">{rate} IRR/CAD</span>", "higherRate": "Higher Rate", "lowerRate": "Lower Rate", "noAdjustment": "No Adjustment", "penaltyPayMore": "{adjustment}% Penalty: They Pay More", "penaltyReceiveLess": "{adjustment}% Penalty: They Receive Less", "bonusPayLess": "{adjustment}% Bonus: They Pay Less", "bonusReceiveMore": "{adjustment}% Bonus: They Receive More", "selectOfferTypeAndRate": "Select offer type and enter base rate to see calculations.", "selectOfferTypeForDetails": "Select an offer type to see specific details.", "createOffer": "Create Offer", "updateOffer": "Update Offer", "tierLabel": "Tier {tier}", "lowerTiersDefault": "Lower Tiers (1-2)", "higherTiersDefault": "Higher Tiers (4-5)", "noLowerTiers": "No Lower Tiers", "noHigherTiers": "No Higher Tiers", "tierSingle": "Tier {tier}", "tiersRange": "Tiers {start}-{end}", "lowerTiers": "Lower Tiers", "higherTiers": "Higher Tiers", "lowerTierUsers": "lower tier users", "higherTierUsers": "higher tier users", "they": "they", "noOne": "no one", "lowerTierSellExplanation": "If you are <b>SELL</b>ing CAD, {entity} pay <span class=\"text-worse\">more</span> IRR for each CAD.", "lowerTierBuyExplanation": "If you are <b>BUY</b>ing CAD, {entity} receive <span class=\"text-worse\">less</span> IRR for each CAD.", "higherTierSellExplanation": "If you are <b>SELL</b>ing CAD, {entity} pay <span class=\"text-better\">less</span> IRR for each CAD.", "higherTierBuyExplanation": "If you are <b>BUY</b>ing CAD, {entity} receive <span class=\"text-better\">more</span> IRR for each CAD.", "helperText": {"lowerRep": "Enter a positive percentage (≥0%). This makes the rate <span class=\"worse\">worse</span> for {tiersText}. {detail}", "higherRep": "Enter a positive percentage (≥0%). This makes the rate <span class=\"better\">better</span> for {tiersText}. {detail}"}, "helperDetails": {"lowerRepSell": "(They will pay {adjustment}% <span class=\"worse\">more</span> IRR per CAD).", "lowerRepBuy": "(They will receive {adjustment}% <span class=\"worse\">less</span> IRR per CAD).", "higherRepSell": "(They will pay {adjustment}% <span class=\"better\">less</span> IRR per CAD).", "higherRepBuy": "(They will receive {adjustment}% <span class=\"better\">more</span> IRR per CAD)."}, "validation": {"offerTypeRequired": "Offer type is required", "amountRequired": "Amount is required", "amountPositive": "Amount must be positive", "baseRateRequired": "Base rate is required", "baseRatePositive": "Base rate must be positive", "adjustmentNonNegative": "Adjustment must be non-negative", "adjustmentMaxHundred": "Adjustment cannot exceed 100%"}}, "profile": {"title": "Profile", "changePhoto": "Change Photo", "memberSince": "Member since {date}", "accountStatus": "Account Status", "emailVerification": "Email Verification", "phoneVerification": "Phone Verification", "securityScore": "Security Score", "verified": "Verified", "notVerified": "Not Verified", "pendingVerification": "Pending Verification", "resendEmail": "<PERSON><PERSON><PERSON>", "excellentSecurity": "Excellent Security", "goodSecurity": "Good Security", "basicSecurity": "Basic Security", "completePhoneVerification": "Complete Phone Verification", "whyVerifyPhone": "Why verify your phone?", "phoneVerificationBenefit": "Phone verification enhances security and enables secure communication features.", "rateLimitExceeded": "Rate Limit Exceeded", "tooManyAttempts": "Too many attempts. Please wait {seconds} seconds before trying again.", "remainingAttempts": "You have {count} attempts remaining.", "waitBeforeRetrying": "Please wait {seconds} before requesting or verifying again.", "phoneNumberE164": "Phone Number (E.164 format)", "phoneNumberHelpText": "Start with '+' and your country code, followed by the number without spaces or dashes (e.g., +15551234567).", "phoneNumberPlaceholder": "e.g., +15551234567", "sendVerificationCode": "Send Verification Code", "verifyingNumber": "Verifying number:", "verificationCode": "Verification Code", "enterSixDigitCode": "Enter 6-digit code", "verifyCode": "Verify Code", "resendCode": "Resend Code", "resendCodeWait": "Resend Code ({seconds}s)", "changeNumber": "Change Number", "phoneVerified": "Phone Verified!", "phoneVerifiedSuccess": "Your phone number has been successfully verified.", "verifiedNumber": "Verified number: {number}", "profileStatistics": "Profile Statistics", "reputationScore": "Reputation Score", "activeOffers": "Active Offers", "completedTransactions": "Completed Transactions", "quickActions": "Quick Actions", "createNewOffer": "Create <PERSON>er", "viewMyOffers": "View My Offers", "browseOffers": "Browse Offers", "loadingProfile": "Loading profile...", "clickIfStuck": "Click here if loading is stuck", "bronze": "Bronze", "silver": "Silver", "gold": "Gold", "platinum": "Platinum", "diamond": "Diamond", "defaultUsername": "User", "defaultJoinDate": "May 2025", "validation": {"phoneNumberRequired": "Phone number is required", "phoneNumberFormat": "Must be in E.164 format (e.g., +15551234567)", "otpRequired": "OTP is required", "otpLength": "OTP must be 6 digits", "otpDigitsOnly": "OTP must contain only digits"}, "messages": {"loadProfileError": "Failed to load user profile. Please try again.", "sendOtpError": "Failed to send <PERSON><PERSON>. Please try again.", "verifyOtpError": "Failed to verify <PERSON><PERSON>. Please try again.", "otpSentSuccess": "OTP sent successfully!", "phoneVerifiedSuccess": "Phone verified successfully!", "emailSentSuccess": "Verification email sent successfully. Please check your email.", "resendEmailError": "Failed to resend verification email"}}, "chat": {"title": "Cha<PERSON>", "newMessage": "New Message", "sendMessage": "Send Message", "typeMessage": "Type your message...", "online": "Online", "offline": "Offline", "lastSeen": "Last seen", "messageDelivered": "Message delivered", "messageRead": "Message read", "clearChat": "Clear chat", "deleteMessage": "Delete message", "editMessage": "Edit message", "replyToMessage": "Reply to message", "forwardMessage": "Forward message", "attachFile": "Attach file", "sendImage": "Send image", "chatHistory": "Chat history", "noChatHistory": "No chat history", "loadingMessages": "Loading messages...", "connectionLost": "Connection lost", "reconnecting": "Reconnecting...", "connected": "Connected", "disconnected": "Disconnected", "errorLoadingChat": "Error Loading Chat", "noMessagesYet": "No messages yet. Start the conversation!", "messagesSent": "Message sent", "messageFailed": "Failed to send message", "transactionDetails": "Transaction Details", "buyingCurrencyExchange": "Buying currency exchange", "sellingCurrencyExchange": "Selling currency exchange", "exchangeInProgress": "Exchange in progress", "waitingForDetails": "Waiting for details..."}, "notifications": {"title": "Notifications", "notifications": "Notifications", "markAsRead": "<PERSON> as read", "markAllAsRead": "Mark all as read", "markRead": "<PERSON> read", "deleteNotification": "Delete notification", "clearAll": "Clear all", "noNotifications": "No notifications", "newNotification": "New Notification", "noTimestamp": "No timestamp", "invalidDate": "Invalid Date", "accept": "Accept", "decline": "Decline", "failedToMarkAsRead": "Failed to mark notification as read.", "failedToMarkAllAsRead": "Failed to mark all notifications as read.", "allMarkedAsRead": "All notifications marked as read.", "cannotAcceptInvalidId": "Cannot accept: Interest ID missing or invalid in notification.", "interestAcceptedSuccess": "Interest accepted! Chat session should be available.", "failedToAcceptInterest": "Failed to accept interest.", "cannotDeclineEssentialMissing": "Cannot decline: Essential interest information missing from notification.", "newInterestOnOffer": "New Interest on Your Offer!", "yourInterestAccepted": "Your Interest Was Accepted!", "yourInterestDeclined": "Your Interest Was Declined", "offerStatusUpdated": "Offer Status Updated", "yourOfferStatusChanged": "Your Offer Status Changed", "defaultNotificationMessage": "You have a new update.", "offerStatusChanged": "The status of offer \"{offerTitle}\" changed to {newStatus}.", "ownerOfferStatusChanged": "You updated the status of your offer \"{offerTitle}\" to {newStatus}.", "newInterest": "New interest", "interestAccepted": "Interest accepted", "interestDeclined": "Interest declined", "offerUpdated": "Offer updated", "offerCancelled": "Offer cancelled", "paymentReceived": "Payment received", "paymentSent": "Payment sent", "transactionCompleted": "Transaction completed", "verificationRequired": "Verification required", "systemMaintenance": "System maintenance", "newFeature": "New feature", "noNotificationsDescription": "You have no notifications at the moment. Check back later for updates."}, "transaction": {"title": "Transaction", "pending": "Pending", "inProgress": "In Progress", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled", "disputed": "Disputed", "refunded": "Refunded", "paymentProof": "Payment Proof", "uploadProof": "Upload Proof", "receiptNumber": "Receipt Number", "transactionId": "Transaction ID", "bankName": "Bank Name", "accountNumber": "Account Number", "routingNumber": "Routing Number", "swiftCode": "SWIFT Code", "iban": "IBAN", "transactionDate": "Transaction Date", "processingTime": "Processing Time", "estimatedTime": "Estimated Time", "fees": "Fees", "exchangeRate": "Exchange Rate", "totalCost": "Total Cost", "amountSent": "Amount <PERSON>", "amountReceived": "Amount Received", "confirmPayment": "Confirm Payment", "disputeTransaction": "Dispute Transaction", "rateExperience": "Rate Experience", "transactionHistory": "Transaction History", "noTransactions": "No transactions"}, "transactionTimeline": {"transactionInitiated": "Transaction Initiated", "agreedToTerms": "{username} Agreed to Terms", "firstPayerDesignated": "First Payer Designated: {username}", "firstPayerDesignatedContent": "{username} will pay first.", "declaredPayment": "{username} Declared Payment", "trackingInfo": "Tracking: {trackingInfo}", "noTrackingInfo": "No tracking info provided.", "confirmedReceipt": "{username} Confirmed Receipt", "confirmedFinalReceipt": "{username} Confirmed Final Receipt", "transactionCompleted": "Transaction Completed", "transactionCancelled": "Transaction Cancelled by {username}", "transactionDisputed": "Transaction Disputed by {username}", "reason": "Reason: {reason}", "noReason": "No reason provided.", "noTransactionData": "No transaction data for timeline.", "unknownUser": "Unknown User", "notAvailable": "N/A"}, "transactionInfo": {"sellCAD": "Sell CAD", "buyCAD": "Buy CAD", "receives": "receives {amount} IRR", "sells": "sells {amount} CAD", "buys": "buys {amount} CAD", "pays": "pays {amount} IRR", "at": "at"}, "errors": {"networkError": "Network error", "serverError": "Server error", "validationError": "Validation error", "authenticationError": "Authentication error", "authorizationError": "Authorization error", "notFoundError": "Not found", "timeoutError": "Timeout error", "unknownError": "Unknown error", "invalidEmail": "Invalid email", "invalidPhone": "Invalid phone number", "passwordTooShort": "Password too short", "passwordMismatch": "Passwords don't match", "usernameExists": "Username already exists", "emailExists": "Email already exists", "phoneExists": "Phone number already exists", "invalidCredentials": "Invalid credentials", "accountLocked": "Account locked", "accountNotVerified": "Account not verified", "sessionExpired": "Session expired", "pleaseLoginAgain": "Please log in again.", "rateLimitExceeded": "Rate limit exceeded", "fileTooBig": "File too big", "invalidFileType": "Invalid file type", "uploadFailed": "Upload failed", "connectionLost": "Connection lost", "tryAgain": "Try again", "login": "<PERSON><PERSON>", "registration": "Registration Error", "failedToLoadOffer": "Failed to load offer", "offerNotFound": "Offer not found", "offerNotFoundMessage": "The offer you're looking for doesn't exist or has been removed", "failedToUpdateOffer": "Failed to update offer", "failedToExpressInterest": "Failed to express interest", "failedToAcceptInterest": "Failed to accept interest", "failedToDeclineInterest": "Failed to decline interest"}, "validation": {"required": "This field is required", "email": "Enter a valid email", "phone": "Enter a valid phone number", "minLength": "Enter at least {min} characters", "maxLength": "Enter at most {max} characters", "number": "Enter a valid number", "positive": "Enter a positive number", "url": "Enter a valid URL", "pattern": "Enter a valid format", "confirm": "Confirmation doesn't match", "unique": "This value already exists", "range": "Value must be between {min} and {max}", "emailRequired": "Please input your email", "passwordRequired": "Please input your password", "emailInvalid": "Please input a valid email", "passwordInvalid": "Password must be at least {min} characters long", "terms": "You must accept the Terms of Service and Privacy Policy", "usernameMinLength": "Username must be at least {min} characters long", "usernameMaxLength": "Username must be at most {max} characters long", "usernameInvalidChars": "Username can only contain letters, numbers, underscores, and hyphens", "usernameInvalidFormat": "Username cannot start or end with underscore or hyphen"}, "currency": {"irr": "Iranian Rial", "usd": "US Dollar", "eur": "Euro", "gbp": "British Pound", "cad": "Canadian Dollar", "aud": "Australian Dollar", "aed": "UAE Dirham", "chf": "Swiss Franc", "jpy": "Japanese Yen", "cny": "Chinese Yuan", "try": "Turkish Lira", "krw": "South Korean Won", "sek": "Swedish Krona", "nok": "Norwegian Krone", "dkk": "Danish Krone"}, "language": {"fa": "فار<PERSON>ی", "en": "English", "switchTo": "Switch to", "changeLanguage": "Change Language"}, "common": {"yes": "Yes", "no": "No", "ok": "OK", "done": "Done", "pending": "Pending", "processing": "Processing", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled", "expired": "Expired", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "public": "Public", "private": "Private", "visible": "Visible", "hidden": "Hidden", "available": "Available", "unavailable": "Unavailable", "online": "Online", "offline": "Offline", "connected": "Connected", "disconnected": "Disconnected", "syncing": "Syncing", "synced": "Synced", "loading": "Loading", "loaded": "Loaded", "empty": "Empty", "full": "Full", "new": "New", "old": "Old", "updated": "Updated", "created": "Created", "deleted": "Deleted", "modified": "Modified", "unchanged": "Unchanged", "switchToLight": "Switch to light mode", "switchToDark": "Switch to dark mode", "cancel": "Cancel"}, "homeView": {"heroTitle": "Welcome to ArzAni", "heroSubtitle": "Connect with your community through secure peer-to-peer transactions", "createOffer": "Create an Offer", "browseOffers": "Browse Offers", "totalOffers": "Total Offers", "activeOffersLabel": "Active Offers", "myOffers": "My Offers", "reputationLevel": "Reputation Level", "quickActions": "Quick Actions", "myOffersAction": "My Offers", "myOffersDescription": "Manage your existing offers and view interest requests", "viewMyOffers": "View My Offers", "profile": "Profile", "profileDescription": "Update your profile information and payment details", "viewProfile": "View Profile", "recentActivity": "Recent Offers", "buying": "Buying", "selling": "Selling", "failedToLoadData": "Failed to load homepage data. Please refresh the page.", "atRate": "at {rate} rate"}, "level": {"title": "Level", "current": "Level {value}", "1": "Level 1", "2": "Level 2", "3": "Level 3", "4": "Level 4", "5": "Level 5"}, "transactionFlow": {"paymentDetails": {"accountHolderName": "Account Holder Name", "bankName": "Bank Name", "recipientDetails": "Recipient Payment Details", "accountNumber": "Account Number", "maskedAccount": "****{lastDigits}", "institutionName": "Institution", "additionalInfo": "Additional Information", "noDetailsAvailable": "Payment details will be available once negotiation is finalized.", "paymentDueDate": "Payment Due:"}, "title": "Transaction Flow", "loading": "Loading transaction details...", "error": "Unable to load transaction", "transactionId": "Transaction ID", "steps": {"designate": "Designate Payer", "designateDescription": "Decide who pays first", "payment1": "Payment 1", "payment1Description": "First party pays", "confirmation1": "Confirmation 1", "confirmation1Description": "Second party confirms", "payment2": "Payment 2", "payment2Description": "Second party pays", "confirmation2": "Confirmation 2", "confirmation2Description": "First party confirms", "completed": "Completed", "completedDescription": "Transaction finished"}, "userRoles": {"providerOf": "Provider of {currency}", "loadingRole": "Loading role...", "userDataMissing": "User data missing in transaction...", "determiningRole": "Determining role..."}, "currentAction": {"loadingAction": "Loading...", "awaitingDesignation": "Awaiting First Payer Designation", "yourTurnToPay": "Your Turn to Pay", "yourTurnToConfirm": "Your Turn to Confirm Payment", "waitingForPayment": "Waiting for {username} to Pay", "waitingForConfirmation": "Waiting for {username} to Confirm", "transactionCompleted": "Transaction Successfully Completed", "transactionCancelled": "Transaction Cancelled", "transactionDisputed": "Transaction Under Dispute"}, "actionInfo": {"designationPending": "Both parties need to agree on who pays first. Use the negotiation tools below to reach an agreement.", "youPayFirst": "You have been designated to pay first. Please send {amount} {currency} to {username} and then declare your payment.", "youConfirmPayment": "Please confirm that you received {amount} {currency} from {username}.", "otherPartyPaying": "{username} will pay {amount} {currency} to you first. You will confirm once received.", "otherPartyConfirming": "Waiting for {username} to confirm they received your payment of {amount} {currency}.", "youPaySecond": "Now it's your turn to pay {amount} {currency} to {username}.", "youConfirmSecond": "Please confirm that you received the final payment of {amount} {currency} from {username}.", "waitingSecondPayment": "Waiting for {username} to send you {amount} {currency}.", "waitingSecondConfirmation": "Waiting for {username} to confirm receipt of your payment.", "completedExchange": "The exchange of <strong>{amountA} {currencyA}</strong> for <strong>{amountB} {currencyB}</strong> is complete.", "cancellationReason": "Reason", "disputeReason": "Reason", "noReasonProvided": "No reason provided.", "adminReview": "An admin will review.", "followInstructions": "Please follow the instructions."}, "subSteps": {"agreementReached": "Agreement reached on first payer", "paymentInfoProvided": "Payment information provided by both parties", "firstPaymentDeclared": "First payment declared by {username}", "firstPaymentConfirmed": "First payment confirmed by {username}", "secondPaymentDeclared": "Second payment declared by {username}", "secondPaymentConfirmed": "Final payment confirmed by {username}", "allStepsCompleted": "All transaction steps completed successfully", "transactionWasCancelled": "Transaction was cancelled", "transactionWasDisputed": "Transaction was disputed", "designateFirstPayer": "Designate who pays first", "userMakesPayment": "{username} makes payment", "userConfirmsReceipt": "{username} confirms receipt", "transactionComplete": "Transaction Complete!", "transactionCancelled": "Transaction Cancelled", "transactionDisputed": "Transaction Disputed"}, "buttons": {"declarePayment": "Declare Payment", "confirmReceipt": "Confirm Receipt", "cancelTransaction": "Cancel Transaction", "disputeTransaction": "Dispute Transaction", "agreeToSystemRecommendation": "Agree to System Recommendation", "agreeToUserProposal": "Agree to {username}'s Proposal", "proposeOtherPaysFirst": "Propose {username} Pays First Instead", "useProfileDetails": "Use Profile Payment Details"}, "timer": {"timeRemaining": "Time remaining:", "timeElapsed": "Time elapsed:", "paymentWindow": "Payment deadline:", "confirmationWindow": "Confirmation window:", "expired": "EXPIRED"}, "negotiation": {"systemRecommendation": "System recommends <strong>{username}</strong> pays first.", "systemRecommendationReason": "Reason: {reason}", "reasonReputation": "{username} has a lower reputation level and should pay first.", "reasonCurrency": "{username} is providing {currency} and should pay first.", "reasonCurrencyGeneric": "{username} is providing the local currency and should pay first.", "reasonOfferCreator": "{username} created the offer and should pay first.", "proposalFrom": "Proposal from {username}", "systemProposal": "System Recommendation", "waitingForResponse": "Waiting for the other party to respond to your proposal.", "bothPartiesAgreed": "Both parties have agreed to the current proposal.", "negotiationFinalized": "Negotiation has been finalized.", "youAgreed": "You have agreed to this proposal.", "otherPartyAgreed": "{username} has agreed to this proposal.", "agreementStatus": "Agreement Status", "pendingResponse": "Pending Response", "finalized": "Finalized", "youAgreedWaitingForFinalize": "You agreed to {username}'s proposal. Waiting for them to finalize or for system processing.", "awaitingYourResponse": "Awaiting your response to {username}'s proposal.", "youAgreedSystemWaiting": "You agreed to the system recommendation. Waiting for {username}.", "otherPartyAgreedSystemWaiting": "{username} agreed to the system recommendation. Waiting for you.", "bothPartiesAgreedFinalizing": "Both parties agreed to the system recommendation. Finalizing...", "agreementReached": "Agreement reached! {username} will pay first.", "negotiationStatus": "Negotiation Status", "proposalActive": "Proposal Active", "agreementReachedTitle": "Agreement Reached", "proposal": "Proposal", "usersProposal": "{username}'s Proposal", "userProposal": "Proposal from User", "reasonLabel": "Reason", "proposedPayer": "Proposed payer: {username}", "systemRecommendationDetermining": "System is determining recommendation..."}, "messages": {"paymentDeclared": "Payment has been declared successfully", "paymentConfirmed": "Payment receipt has been confirmed", "transactionCancelled": "Transaction has been cancelled", "transactionDisputed": "Transaction dispute has been submitted", "proposalSubmitted": "Proposal submitted successfully", "agreementConfirmed": "You have agreed to the current proposal", "paymentDetailsSubmitted": "Payment receiving details submitted successfully", "profileDetailsConfirmed": "Profile details confirmed for this transaction", "errorOccurred": "An error occurred. Please try again.", "paymentGateRequired": "Please provide your payment information to continue", "waitingForOtherParty": "Waiting for the other party to provide their payment information"}, "modals": {"declarePaymentTitle": "Declare Payment Sent", "trackingNumberLabel": "Tracking Number (optional)", "trackingNumberPlaceholder": "Enter tracking number if available", "cancelTransactionTitle": "Cancel Transaction", "cancelReasonLabel": "Reason for cancellation", "cancelReasonPlaceholder": "Please provide a reason for cancelling", "disputeTransactionTitle": "Dispute Transaction", "disputeReasonLabel": "Reason for dispute", "disputeReasonPlaceholder": "Please describe the issue", "proposalTitle": "Propose {username} Pays First", "proposalExplanation": "You are proposing that {username} should pay first instead of you.", "proposalMessageLabel": "Message to support your proposal (optional)", "proposalMessagePlaceholder": "Explain why you think {username} should pay first (e.g., 'I think you should pay first since you have higher reputation')", "submitProposal": "Propose {username} Pays First"}}, "debug": {"reportIssue": "Report Issue", "reportType": "Report Type", "severityText": "Severity", "tagsText": "Tags", "tagsDescription": "Add custom tags to help categorize this report", "tags": {"clickHint": "Tip: Click on the colored tags above to quickly add them to your report", "clickToAdd": "Click to add this tag to your report", "clickToRemove": "Click to remove this tag from your report", "predefined": {"urgent": "<PERSON><PERSON>", "fix-needed": "<PERSON>x Needed", "error": "Error", "enhancement": "Enhancement", "new-feature": "New Feature", "idea": "Idea", "slow": "Slow", "optimization": "Optimization", "speed": "Speed", "design": "Design", "user-experience": "User Experience", "interface": "Interface", "better-way": "Better Way", "suggestion": "Suggestion", "help": "Help", "unclear": "Unclear", "documentation": "Documentation", "miscellaneous": "Miscellaneous", "general": "General"}}, "title": "Title", "description": "Description", "stepsToReproduce": "Steps to Reproduce", "expectedBehavior": "Expected Behavior", "actualBehavior": "Actual Behavior", "additionalNotes": "Additional Notes", "contextInfo": "Context Information", "currentPage": "Current Page", "logEntries": "Log Entries", "userActions": "User Actions", "viewport": "Viewport", "recentActions": "Recent Actions", "sendReport": "Send Report", "selectSeverity": "Select severity level", "titlePlaceholder": "Brief summary of the issue", "descriptionPlaceholder": "Detailed description of what happened", "stepsPlaceholder": "1. Click on...\n2. Navigate to...\n3. Expected result...", "expectedPlaceholder": "What you expected to happen", "actualPlaceholder": "What actually happened", "additionalNotesPlaceholder": "Any additional context or information", "formValidationError": "Please fill in the required fields", "reportSentSuccess": "Report sent successfully! Report ID: {reportId}", "reportSentError": "Failed to send report. Please try again.", "reportTypes": {"bug": "Bug Report", "bugDescription": "Something is broken or not working as expected", "feature": "Feature Request", "featureDescription": "Suggest a new feature or enhancement", "performance": "Performance Issue", "performanceDescription": "App is slow or unresponsive", "uiux": "UI/UX Issue", "uiuxDescription": "Interface design or usability problem", "improvement": "General Improvement", "improvementDescription": "Suggestion for making something better", "question": "Question/Help", "questionDescription": "Need help or clarification", "other": "Other", "otherDescription": "Something else not covered above"}, "severity": {"low": "Low", "medium": "Medium", "high": "High", "critical": "Critical"}, "resetForm": "Reset Form", "resetFormConfirm": "Are you sure you want to clear all form data? This action cannot be undone.", "resetFormSuccess": "Form has been reset successfully.", "offlineReportStored": "You're offline. Report saved locally and will be submitted when connection is restored.", "offlineReportsSubmitted": "{count} offline report(s) submitted successfully.", "offlineReportsFailed": "{count} offline report(s) failed to submit after multiple attempts.", "draftSaved": "Draft saved automatically.", "draftLoaded": "Previous draft restored.", "draftCleared": "Draft cleared.", "hasDrafts": "You have {count} saved draft(s). Would you like to restore the latest one?", "restoreDraft": "Restore Draft", "discardDraft": "Start Fresh", "autoSaveEnabled": "Auto-save is enabled. Your progress is automatically saved.", "connectionRestored": "Connection restored. Processing offline reports...", "offlineMode": "You're currently offline. Reports will be saved locally.", "offlineReportsCount": "{count} offline report(s) pending submission", "processingOfflineReports": "Processing offline reports..."}, "debugReport": {"sendReport": "Send Debug Report", "modalTitle": "Debug Report", "modalDescription": "This will send application logs and diagnostic information to help developers identify and fix issues. No personal data will be included.", "totalLogs": "Total Logs", "currentPage": "Current Page", "sessionId": "Session ID", "browser": "Browser", "logLevels": "Log Levels", "additionalNotes": "Additional Notes (Optional)", "notesPlaceholder": "Describe what you were doing when the issue occurred, or provide any additional context that might help...", "sendSuccess": "Debug report sent successfully! Thank you for helping us improve the application.", "sendError": "Failed to send debug report. Please try again or contact support.", "noLogsWarning": "No logs available to send."}, "voice": {"recordBugReport": "🎤 Record Bug Report with AI", "aiEnabled": "AI Available", "aiDisabled": "AI Unavailable", "notSupported": "Voice recording is not supported in your browser. Please use a modern browser with microphone support.", "permissionDenied": "Microphone access denied. Please allow microphone access to record voice reports.", "noMicrophone": "No microphone found. Please connect a microphone and try again.", "recordingError": "Recording failed. Please try again.", "maxDurationReached": "Maximum recording duration of {duration} seconds reached.", "paused": "Paused", "nearMaxDuration": "{remaining}s remaining", "playback": "Play", "delete": "Delete", "transcribing": "Converting speech to text...", "analyzing": "AI is analyzing your report...", "processing": "Processing...", "transcription": "Voice Transcription", "aiGeneratedReport": "AI Generated Report", "confidence": "{confidence}% confidence", "applyToForm": "Apply to Form", "transcriptionComplete": "Voice transcription completed successfully!", "analysisComplete": "AI analysis completed with {confidence}% confidence!", "reportApplied": "AI-generated report applied to form successfully!", "processingError": "Voice processing failed. Please try again.", "playbackError": "Audio playback failed.", "serviceNotAvailable": "AI voice service is not available. Please fill out the form manually."}, "ai": {"serviceNotAvailable": "AI service is not available. Please check your connection and try again.", "transcriptionError": "Speech transcription failed. Please try recording again.", "analysisError": "AI analysis failed. Please try again or fill out the form manually.", "processingError": "AI processing failed. Please try again.", "analysisComplete": "AI analysis completed with {confidence}% confidence!"}}