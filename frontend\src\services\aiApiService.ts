import axios from 'axios';

/**
 * AI API Service
 * Handles communication with the backend AI endpoints for voice processing and analysis
 */

// Types for API requests and responses
export interface AudioTranscriptionRequest {
  audioData: string;
  mimeType: string;
  duration: number;
  language?: string;
}

export interface AudioTranscriptionResponse {
  success: boolean;
  transcription?: string;
  confidence?: number;
  language?: string;
  error?: string;
}

export interface AiAnalysisRequest {
  transcription: string;
  language?: string;
  userContext?: {
    currentPage?: string;
    userAgent?: string;
    viewport?: {
      width: number;
      height: number;
    };
  };
}

export interface AiGeneratedReport {
  title: string;
  description: string;
  stepsToReproduce?: string;
  expectedBehavior?: string;
  actualBehavior?: string;
  additionalNotes?: string;
  suggestedSeverity: 'low' | 'medium' | 'high' | 'critical';
  suggestedType: 'bug' | 'feature-request' | 'performance' | 'ui-ux' | 'improvement' | 'question' | 'other';
  suggestedTags?: string[];
  confidence: number;
}

export interface AiAnalysisResponse {
  success: boolean;
  generatedReport?: AiGeneratedReport;
  originalTranscription?: string;
  processingTime?: number;
  error?: string;
}

export interface VoiceToReportRequest {
  audioData: string;
  mimeType: string;
  duration: number;
  language?: string;
  userContext?: {
    currentPage?: string;
    userAgent?: string;
    viewport?: {
      width: number;
      height: number;
    };
  };
}

export interface VoiceToReportResponse {
  success: boolean;
  transcription?: string;
  generatedReport?: AiGeneratedReport;
  processingTime?: number;
  error?: string;
}

export interface AiStatusResponse {
  success: boolean;
  status?: {
    aiServiceAvailable: boolean;
    transcriptionServiceAvailable: boolean;
    supportedAudioFormats: string[];
    bestAudioFormat: string;
    maxAudioDuration: number;
    supportedLanguages: string[];
    features: {
      transcription: boolean;
      aiAnalysis: boolean;
      voiceToReport: boolean;
    };
  };
  error?: string;
}

export interface AiConfigResponse {
  success: boolean;
  config?: {
    maxAudioDuration: number;
    supportedLanguages: string[];
    supportedAudioFormats: string[];
    recommendedAudioFormat: string;
    features: {
      transcription: boolean;
      aiAnalysis: boolean;
      voiceToReport: boolean;
    };
  };
  error?: string;
}

class AiApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';
  }

  /**
   * Get AI service status
   */
  async getStatus(): Promise<AiStatusResponse> {
    try {
      const response = await axios.get(`${this.baseUrl}/api/ai/status`);
      return response.data;
    } catch (error: any) {
      console.error('Failed to get AI status:', error);
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to get AI status',
      };
    }
  }

  /**
   * Get AI configuration
   */
  async getConfig(): Promise<AiConfigResponse> {
    try {
      const response = await axios.get(`${this.baseUrl}/api/ai/config`);
      return response.data;
    } catch (error: any) {
      console.error('Failed to get AI config:', error);
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to get AI config',
      };
    }
  }

  /**
   * Transcribe audio to text
   */
  async transcribeAudio(request: AudioTranscriptionRequest): Promise<AudioTranscriptionResponse> {
    try {
      const response = await axios.post(`${this.baseUrl}/api/ai/transcribe`, request, {
        timeout: 30000, // 30 second timeout
      });
      return response.data;
    } catch (error: any) {
      console.error('Audio transcription failed:', error);
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Audio transcription failed',
      };
    }
  }

  /**
   * Analyze transcription and generate bug report
   */
  async analyzeTranscription(request: AiAnalysisRequest): Promise<AiAnalysisResponse> {
    try {
      const response = await axios.post(`${this.baseUrl}/api/ai/analyze`, request, {
        timeout: 45000, // 45 second timeout for AI analysis
      });
      return response.data;
    } catch (error: any) {
      console.error('AI analysis failed:', error);
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'AI analysis failed',
      };
    }
  }

  /**
   * Process voice recording to generate bug report (combined transcription + analysis)
   */
  async processVoiceToReport(request: VoiceToReportRequest): Promise<VoiceToReportResponse> {
    try {
      const response = await axios.post(`${this.baseUrl}/api/ai/voice-to-report`, request, {
        timeout: 60000, // 60 second timeout for combined processing
      });
      return response.data;
    } catch (error: any) {
      console.error('Voice-to-report processing failed:', error);
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Voice-to-report processing failed',
      };
    }
  }
}

// Export singleton instance
export const aiApiService = new AiApiService();
