<template>
  <div class="debug-dashboard-custom">
    <DebugDashboardHeader
      :loading="loading"
      @refresh="handleRefresh"
      @toggle-theme="handleToggleTheme"
    />

    <main class="dashboard-main-content">
      <DebugReportFilters
        :initial-filters="filters"
        :report-type-options="reportTypeOptions"
        :severity-options="severityOptions"
        @apply-filters="handleApplyFilters"
        @clear-filters="handleClearFilters"
      />

      <DebugReportList
        :reports="reports"
        :total-reports="total"
        :loading="loading"
        :error="error"
        :current-page="currentPage"
        :total-pages="totalPages"
        :page-size="limit"
        :sort-by="sort.sortBy"
        :sort-order="sort.sortOrder"
        @view-report="handleViewReport"
        @download-report="handleDownloadReport"
        @change-page="handleChangePage"
        @change-page-size="handleChangePageSize"
        @sort="handleSort"
        @refresh-reports="handleRefresh"
      />
    </main>

    <!-- Modal for report details -->
    <div class="modal-overlay" v-if="showDetailsModal" @click="closeModal">
      <div class="modal-container" @click.stop>
        <div class="modal-header">
          <h2>Debug Report Details</h2>
          <button class="btn-close-modal" @click="closeModal">&times;</button>
        </div>
        <div class="modal-content">
          <ReportDetailsModal
            v-if="selectedReport"
            :report="selectedReport"
            :show="showDetailsModal"
            @update:show="showDetailsModal = $event"
            @close="closeModal"
            @export="handleDownloadReport"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useAdminDebugStore } from '../../stores/adminDebugStore';
import { useThemeStore } from '../../stores/theme';

// Import child components
import DebugDashboardHeader from './components/DebugDashboardHeader.vue';
import DebugReportFilters from './components/DebugReportFilters.vue';
import DebugReportList from './components/DebugReportList.vue';
import ReportDetailsModal from '../../components/admin/ReportDetailsModal.vue';

// Type definitions (adjust imports based on your actual types location)
interface ParsedReport {
  reportId?: string;
  reportType?: string;
  reportSeverity?: string;
  serverReceivedAt?: string;
  reportDescription?: string;
}

interface ReportFiltersPayload {
  searchQuery?: string;
  type?: string;
  severity?: string;
  dateStart?: string;
  dateEnd?: string;
}

interface ReportSortPayload {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

// Report types that match the backend validation schema and actual data
const REPORT_TYPES = {
  'bug': 'Bug',
  'feature-request': 'Feature Request',
  'performance': 'Performance',
  'ui-ux': 'UI/UX',
  'improvement': 'Improvement',
  'question': 'Question',
  'other': 'Other'
};

const REPORT_SEVERITIES = {
  'low': 'Low',
  'medium': 'Medium',
  'high': 'High',
  'critical': 'Critical'
};

defineOptions({
  name: 'DebugDashboardViewCustom'
});

const adminDebugStore = useAdminDebugStore();

const {
  reports,
  total,
  totalPages,
  currentPage,
  limit,
  loading,
  error,
  selectedReport,
  filters,
  sort,
} = storeToRefs(adminDebugStore);

const showDetailsModal = ref(false);

const reportTypeOptions = computed(() =>
  Object.entries(REPORT_TYPES).map(([value, label]) => ({ label, value }))
);

const severityOptions = computed(() =>
  Object.entries(REPORT_SEVERITIES).map(([value, label]) => ({ label, value }))
);

onMounted(() => {
  adminDebugStore.fetchReports();
});

function handleRefresh() {
  adminDebugStore.refresh();
}

function handleToggleTheme() {
  const themeStore = useThemeStore();
  themeStore.toggleTheme();
}

function handleApplyFilters(newFilters: ReportFiltersPayload) {
  adminDebugStore.updateFilters(newFilters);
  adminDebugStore.fetchReports(true); // Reset to page 1
}

function handleClearFilters() {
  adminDebugStore.updateFilters({
    type: undefined,
    severity: undefined,
    dateStart: undefined,
    dateEnd: undefined,
    searchQuery: undefined,
  });
  adminDebugStore.fetchReports(true); // Reset to page 1
}

function handleViewReport(report: ParsedReport) {
  if (!report.reportId) return;
  adminDebugStore.fetchReportById(report.reportId);
  showDetailsModal.value = true;
}

async function handleDownloadReport(reportId: string) {
  try {
    await adminDebugStore.downloadReport(reportId);
    // Add success notification if your app has a notification system
  } catch (err) {
    console.error('Failed to download report:', err);
    // Add error notification if your app has a notification system
  }
}

function handleChangePage(page: number) {
  adminDebugStore.setCurrentPage(page);
  adminDebugStore.fetchReports();
}

function handleChangePageSize(newPageSize: number) {
  adminDebugStore.setLimit(newPageSize);
  adminDebugStore.fetchReports(true); // Reset to page 1
}

function handleSort(newSort: ReportSortPayload) {
  adminDebugStore.updateSort(newSort);
  adminDebugStore.fetchReports();
}

function closeModal() {
  showDetailsModal.value = false;
  adminDebugStore.clearSelectedReport();
}
</script>

<style scoped>
.debug-dashboard-custom {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-base, #f0f2f5);
  color: var(--text-primary, #333);
}

.dashboard-main-content {
  flex-grow: 1;
  padding: 1rem;
  max-width: 1440px;
  margin: 0 auto;
  width: 100%;
}

@media (max-width: 768px) {
  .dashboard-main-content {
    padding: 0.5rem;
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-container {
  background-color: var(--bg-surface, #fff);
  padding: 1.5rem;
  border-radius: var(--radius-lg, 8px);
  box-shadow: var(--shadow-xl, 0 20px 25px -5px rgba(0,0,0,0.1));
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-base, #e0e0e0);
}

.modal-header h2 {
  margin: 0;
  font-size: var(--font-size-xl, 1.25rem);
  color: var(--text-primary, #333);
}

.btn-close-modal {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary, #666);
  padding: 0.25rem;
  border-radius: var(--radius-sm, 4px);
  transition: all 0.2s ease-in-out;
}

.btn-close-modal:hover {
  background-color: var(--bg-surface-hover, #f8f9fa);
  color: var(--text-primary, #333);
}

.modal-content {
  overflow-y: auto;
  flex-grow: 1;
}

/* Dark theme support */
[data-theme="dark"] .debug-dashboard-custom {
  background-color: var(--bg-base-dark, #111827);
  color: var(--text-primary-dark, #f9fafb);
}

[data-theme="dark"] .modal-container {
  background-color: var(--bg-surface-dark, #1f2937);
}

[data-theme="dark"] .modal-header {
  border-bottom-color: var(--border-base-dark, #374151);
}

[data-theme="dark"] .modal-header h2 {
  color: var(--text-primary-dark, #f9fafb);
}

[data-theme="dark"] .btn-close-modal {
  color: var(--text-secondary-dark, #d1d5db);
}

[data-theme="dark"] .btn-close-modal:hover {
  background-color: var(--bg-surface-hover-dark, #374151);
  color: var(--text-primary-dark, #f9fafb);
}
</style>